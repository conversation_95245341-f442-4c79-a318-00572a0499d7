<?php

namespace App\Service\TaskCron;

use App\Entity\Announcement;
use App\Entity\TaskCourse;
use App\Entity\TaskCourseGroup;
use App\Entity\User;
use App\Service\Annoucement\Admin\AnnouncementGroupService;
use App\Utils\TimeZoneConverter\TimeZoneConverter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

class TaskCourseService
{
    private EntityManagerInterface $em;
    private AnnouncementGroupService $announcementGroupService;
    private Security $security;

    public function __construct(EntityManagerInterface $em, Security $security, AnnouncementGroupService $announcementGroupService)
    {
        $this->em = $em;
        $this->security = $security;
        $this->announcementGroupService = $announcementGroupService;
    }

    public function getAnnouncementTasks(Announcement $announcement): array
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $filterByTutor = $user->isTutor() && !($user->isAdmin() || $user->isManager());

        /** Fill group basic information */
        $allowedGroups = [];
        foreach ($announcement->getAnnouncementGroups() as $group)
        {
            $tutor = $group->getAnnouncementTutor();
            if ($tutor && $filterByTutor){
                $tutorUser = $tutor->getTutor();
                if ($tutorUser->getId() === $user->getId())
                {
                    $allowedGroups[] = $group->getId();
                }
            } else $allowedGroups[] = $group->getId();

        }

        $queryBuilder = $this->em->getRepository(TaskCourse::class)->createQueryBuilder('tc')
            ->select('tc.id', 'tc.title', 'tc.isVisible', 'tc.startDate',
                'tc.dateDelivery', 'count(files_tasks.id) as total_files',
                'u.id as user_id')
            ->leftJoin('tc.filesTasks', 'files_tasks')
            ->leftJoin('tc.createdBy', 'u')
            ->groupBy('tc.id')
            ->orderBy('tc.dateDeliveryAnnouncement', 'ASC');

        if ($filterByTutor)
        {
            $queryBuilder->join('tc.taskCourseGroups', 'tcg')
                ->join('tcg.announcementGroup', 'ag')
                ->where($queryBuilder->expr()->in('ag.id', $allowedGroups));
        }


        $tasks = (clone $queryBuilder)
            ->addSelect("'announcement' as source", 'a.timezone')
            ->join('tc.announcement', 'a')
            ->andWhere('tc.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();

        $otherTasks = $queryBuilder->andWhere('tc.course =:course')
            ->addSelect("'course' as source")
            ->andWhere('tc.announcement IS NULL')
            ->setParameter('course', $announcement->getCourse())
            ->getQuery()
            ->getResult();


        $tasks = array_merge($tasks, $otherTasks);

        foreach ($tasks as &$task)
        {
            // Fill task additional information
            $task = $this->fillTaskInformation($task, $allowedGroups);
        }

        return $tasks;
    }

    private function fillTaskInformation(array $task, array $allowedGroups): array
    {
        $temp = $task;
        $taskCourseGroup = $this->em->getRepository(TaskCourseGroup::class)->createQueryBuilder('tcg')
            ->select('tcg.id')
            ->addSelect('g.id as groupId', 'g.groupNumber')
            ->join('tcg.announcementGroup', 'g')
            ->join('tcg.taskCourse', 'tc')
            ->where('tc.id = :task_id')
            ->setParameter('task_id', $task['id'])
            ->orderBy('tcg.numGroup', 'ASC')
            ->getQuery()
            ->getResult();
        $idsGroups = [];
        $gNames = [];
        foreach ($taskCourseGroup as $tcg)
        {
            if (in_array($tcg['groupId'], $allowedGroups))
            {
                $idsGroups[] = $tcg['groupId'];
                $gNames[] = $this->announcementGroupService->generateGroupName($tcg['groupNumber']);
            }
        }

        $temp['groups'] = implode(', ', $gNames);
        $temp['idsGroups'] = $idsGroups;

        return $temp;
       // return TimeZoneConverter::checkTimezone($temp);
    }
}
