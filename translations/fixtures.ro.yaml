nps_question.text.description: '<PERSON><PERSON><PERSON><PERSON> părerea ta'
nps_question.nps.description: 'Evaluarea cursului'
type_course.teleformacion.name: E-learning
type_course.teleformacion.description: 'Pentru cursurile e-learning'
type_course.presencial.name: 'În persoană'
type_course.presencial.description: 'Cursuri la fața locului'
type_course.mixto.name: Mixte
type_course.mixto.description: 'Este o combinație de e-learning și formare față în față'
type_course.aula_virtual.name: 'Sal<PERSON> de clasă virtuală'
type_course.aula_virtual.description: '<PERSON><PERSON><PERSON><PERSON> sunt ținute prin videoconferință'
alert_type_tutor.1.name: 'Persoana apelată nu a accesat cursul'
alert_type_tutor.1.description: 'O alertă va fi trimisă tutorelui dacă persoana convocată nu a accesat cursul'
alert_type_tutor.2.name: '50% din apel s-a scurs și 25% din conținut nu a fost finalizat'
alert_type_tutor.2.description: 'O alertă va fi trimisă tutorelui dacă a trecut 50% din apel și 25% din conținut nu a fost finalizat'
alert_type_tutor.3.name: '80% din apel a trecut și 50% din conținut nu a fost finalizat'
alert_type_tutor.3.description: 'O alertă va fi trimisă tutorelui în cazul în care 80% din apel a trecut și 50% din conținut nu a fost finalizat'
alert_type_tutor.4.name: 'Mai sunt doar câteva zile până la sfârșitul apelului și cursul nu a fost finalizat'
alert_type_tutor.4.description: 'Trebuie să evaluați numărul de zile care sunt considerate puține zile'
alert_type_tutor.5.name: 'Persoana apelată a finalizat cursul, dar nu a răspuns la sondaj.'
alert_type_tutor.5.description: 'Dacă platforma are sondaje, tutorele va primi o alertă dacă persoana apelată a finalizat cursul, dar nu a răspuns la sondaj'
alert_type_tutor.6.name: 'Persoana a finalizat cursul, dar nu a descărcat diploma'
alert_type_tutor.6.description: 'O alertă va fi trimisă tutorelui dacă persoana convocată a terminat cursul, dar nu a descărcat diploma.'
announcement_configuration_type.temporalizacion.name: Timing
announcement_configuration_type.temporalizacion.description: 'Pentru a facilita monitorizarea cursului, vom atribui un timp fiecărui bloc de conținuturi și activități, putând astfel să detectăm care dintre participanți lucrează într-un ritm adecvat sau sunt în urmă în procesul de formare'
announcement_configuration_type.curso_bonificado.name: 'Curs subvenționat'
announcement_configuration_type.curso_bonificado.description: 'Cursurile subvenționate sunt cele care se desfășoară prin intermediul Fundației Tripartite și sunt finanțate de întreprinderi prin intermediul contribuțiilor la asigurările sociale.'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'Chat-ul este un instrument de comunicare sincronă care permite participanților la curs să interacționeze în timp real, prin mesaje text.'
announcement_configuration_type.notificaciones.name: Notificări
announcement_configuration_type.notificaciones.description: 'Notificările sunt mesaje trimise participanților la curs pentru a-i informa cu privire la știri sau evenimente importante.'
announcement_configuration_type.mensajeria.name: Mesaje
announcement_configuration_type.mensajeria.description: 'Un sistem de mesagerie este un sistem de comunicare care permite participanților la curs să trimită și să primească mesaje private.'
announcement_configuration_type.foros.name: Forumuri
announcement_configuration_type.foros.description: 'Un forum este un spațiu de comunicare asincronă care permite participanților la curs să schimbe mesaje pe un anumit subiect.'
announcement_configuration_type.diploma.name: Diplomă
announcement_configuration_type.diploma.description: 'Diplomele sunt certificate care sunt acordate participanților la un curs ca dovadă a absolvirii acestuia.'
announcement_configuration_type.tutor_alerts.name: 'Activați alertele tutorilor'
announcement_configuration_type.tutor_alerts.description: 'Alertele sunt mesaje trimise tutorelui unui curs pentru a-l informa cu privire la știri sau evenimente importante.'
announcement_configuration_type.encuesta_satisfaccion.name: 'Sondaj de satisfacție'
announcement_configuration_type.encuesta_satisfaccion.description: 'Sondajele de satisfacție sunt chestionare trimise participanților la curs pentru a afla ce cred aceștia despre curs.'
announcement_configuration_type.finalizar_convocatoria.name: 'Cursul va rămâne activ la sfârșitul apelului'
announcement_configuration_type.finalizar_convocatoria.description: 'Utilizatorul va putea accesa conținutul cursului după terminarea acestuia.'
announcement_configuration_type.firma_digital.name: 'Semnătura digitală'
announcement_configuration_type.firma_digital.description: 'O semnătură digitală este necesară pentru a putea semna pentru participarea la un curs față în față.'
announcement_configuration_type.gestion_costes.name: 'Gestionarea costurilor'
announcement_configuration_type.gestion_costes.description: 'Gestionarea costurilor permite grupurilor să indice costul apelului.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Activați notificările prin e-mail.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Activați notificările normale'
announcement_configuration_type.objetivos_contenidos.name: 'Obiective și conținut'
announcement_configuration_type.objetivos_contenidos.description: 'Obiectivele și conținutul cursului vor fi incluse în diplomă.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'ID-ul studentului va fi inclus pe diplomă'
announcement_configuration_type.template_excel.name: 'Șablon de înregistrare excel'
announcement_configuration_type.template_excel.description: 'Acest model va fi utilizat pentru înscrierea studenților, folosind codul HRBP, în loc de DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Permiteți tutorelui să descarce rapoartele de grup în format ZIP'
announcement_criteria.1.name: 'Numărul minim de capitole de completat'
announcement_criteria.1.description: 'Nota minimă poate fi, de exemplu, 70 din 100'
announcement_criteria.2.name: 'Finalizarea sarcinilor'
announcement_criteria.2.description: 'Controale de evaluare'
announcement_criteria.3.name: 'Timp maxim de inactivitate'
announcement_criteria.3.description: 'De exemplu, utilizatorul nu poate fi inactiv pentru mai mult de 10 minute'
announcement_criteria.4.name: 'Finalizarea activităților'
announcement_criteria.4.description: 'Utilizatorul trebuie să finalizeze activitățile propuse'
announcement_criteria.5.name: 'Ore de formare finalizate'
announcement_criteria.5.description: 'De exemplu, dacă programul are 20 de ore, utilizatorul trebuie să efectueze cele 20 de ore'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Primul pas în crearea apelului'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'A doua etapă a creării apelului, în care sunt completate informațiile generale'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Acest pas depinde de faptul dacă clientul a activat bonusul'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'Aici sunt adăugați la apel studenți, care pot fi repartizați unui grup'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'În acest pas, sunt configurate grupurile de studenți create în pasul anterior'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'În acest pas, se configurează sondajul care urmează să fie trimis elevilor'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'În acest pas, se configurează sondajul care urmează să fie trimis elevilor'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'În această etapă, diplomele disponibile pe platformă sunt afișate pentru ca clientul să o selecteze pe cea dorită'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Aceste alerte sunt alerte speciale pentru a informa tutorele cu privire la evenimentele din apel'
class_room_virtual.zoom.name: Zoom
class_room_virtual.zoom.description: 'Platformă de conferințe web online, care permite apeluri video de înaltă definiție, cu funcționalități de partajare a desktop-ului, whiteboard, chat, înregistrarea conferințelor, partajarea documentelor și acces de oriunde, deoarece este disponibilă pentru dispozitive mobile.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'Platforma ClickMeeting este una dintre cele mai ușor de utilizat interfețe webinar de pe piață și oferă o mulțime de opțiuni flexibile de personalizare'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Soluție Open Source pentru conferințe video cu conexiuni criptate și disponibilă pentru diferite sisteme de operare'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Software de videoconferință open source ușor de integrat și foarte personalizabil'
configuration_cliente_announcement.COMMUNICATION.description: 'Acest lucru permite activarea comunicațiilor în cadrul apelului'
configuration_cliente_announcement.CERTIFICATE.description: 'Acest lucru permite descărcarea diplomelor în cadrul apelului'
configuration_cliente_announcement.SURVEY.description: 'Acest lucru permite activarea sondajelor, pentru a fi puse la dispoziție mai târziu în timpul apelului'
configuration_cliente_announcement.ALERT.description: 'Acest lucru permite activarea alertelor, care vor fi moștenite în secțiunea de alerte a tutorelui'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Permite temporalizarea capitolelor în cadrul unei cereri de propuneri'
configuration_cliente_announcement.BONIFICATION.description: 'Bonus pentru apel, în special pentru apelul fundației tripartite'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Permite accesul la conținutul apelului după încheierea acestuia'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Permite ca semnătura digitală să fie activată în cererea de candidaturi, în special în cazul cursurilor față în față'
configuration_cliente_announcement.COST.description: 'Permiteți clienților să aloce costuri apelului.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Notificări privind activarea apelului (e-mail, notificare frontală)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Această configurație este special concepută pentru clienții Iberostar, astfel încât să nu afecteze fluxul de fonduri'
configuration_cliente_announcement.REPORT.description: 'Permiteți raportarea în cererile de propuneri'
type_course_announcement_step_creation.seleccionar_curso.name: 'Selectați cursul'
type_course_announcement_step_creation.seleccionar_curso.description: 'Selectați cursul pentru care urmează să fie creat apelul'
type_course_announcement_step_creation.convocatoria.name: 'Cerere de candidaturi'
type_course_announcement_step_creation.convocatoria.description: 'Informații privind cererea de propuneri'
type_course_announcement_step_creation.bonificacion.name: Bonus
type_course_announcement_step_creation.bonificacion.description: 'Informații privind cererea de propuneri'
type_course_announcement_step_creation.alumnado.name: Alumni
type_course_announcement_step_creation.alumnado.description: 'Studenții sunt adăugați la curs'
type_course_announcement_step_creation.grupos.name: Grupuri
type_course_announcement_step_creation.grupos.description: 'Informațiile despre grup sunt detaliate, iar tutorele este de asemenea adăugat'
type_course_announcement_step_creation.comunicacion.name: Comunicare
type_course_announcement_step_creation.comunicacion.description: 'Acest pas depinde de faptul dacă clientul a activat sau nu comunicarea.'
type_course_announcement_step_creation.encuesta.name: Anchetă
type_course_announcement_step_creation.encuesta.description: 'Acest pas depinde de faptul dacă clientul a activat sau nu sondajul.'
type_course_announcement_step_creation.diploma.name: Diplomă
type_course_announcement_step_creation.diploma.description: 'Acest pas depinde de faptul dacă clientul are diploma activată sau nu.'
type_course_announcement_step_creation.alertas.name: Alerte
type_course_announcement_step_creation.alertas.description: 'Acest lucru poate depinde de configurația clientului.'
type_diploma.easylearning.name: Implicit
type_diploma.easylearning.description: 'Este diploma companiei client'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'Este diploma Fundae'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'Este diploma Hobetuz'
type_money.euro.name: Euro
type_money.euro.country: Spania
type_money.dolar_estadounidense.name: 'Dolarul american'
type_money.dolar_estadounidense.country: 'Statele Unite ale Americii'
section_default_front.mi_formacion.name: 'Formarea mea'
section_default_front.mi_formacion.description: 'În cadrul formării atribuite, puteți găsi toate cursurile care v-au fost atribuite.'
section_default_front.formacion_adicional.name: 'Formare suplimentară'
section_default_front.formacion_adicional.description: 'În această secțiune puteți găsi toate cursurile în campus deschis.'
section_default_front.formacion_asignada.name: 'Formare atribuită'
section_default_front.formacion_asignada.description: 'În această secțiune puteți găsi toate cursurile care v-au fost atribuite.'
setting.multi_idioma.name: 'În mai multe limbi'
setting.multi_idioma.description: 'Acesta oferă o interfață multilingvă'
setting.default_lenguage.name: 'Limba implicită'
setting.default_lenguage.description: 'Limba implicită a interfeței de utilizator a sistemului'
setting.languages.name: Limba
setting.languages.description: 'Limbi disponibile în aplicație'
setting.registro_libre.name: 'Înregistrare gratuită a utilizatorilor'
setting.registro_libre.description: 'Modul care permite utilizatorului să se înregistreze liber pe platformă după completarea formularului corespunzător.'
setting.opinion_plataforma.name: 'Platforma Opinii'
setting.opinion_plataforma.description: 'Platforma Opinii'
setting.validacion_automatica.name: 'Validarea automată a înregistrării utilizatorilor'
setting.validacion_automatica.description: 'Validarea automată a înregistrării utilizatorilor'
setting.filtros_plataforma.name: 'Filtre pe platformă'
setting.filtros_plataforma.description: 'Aceasta servește la activarea sau dezactivarea filtrelor de pe platformă'
setting.itinearios_plataforma.name: 'Itinerarii pe platformă'
setting.itinearios_plataforma.description: 'Este vorba despre activarea sau dezactivarea itinerariilor pe platformă'
setting.seccion_cursos.name: 'Secțiuni de curs [FRONT]'
setting.seccion_cursos.description: 'Secțiuni de curs [FRONT],'
setting.set_points_course.name: 'Stabilirea punctelor pentru curs'
setting.set_points_course.description: 'Acesta este utilizat pentru a atribui puncte cursurilor atunci când le creați sau le editați, în special pentru cursul e-learning'
setting.default_points_course.name: 'Puncte implicite pentru curs'
setting.default_points_course.description: 'Dacă se utilizează pentru formule de joc, dacă capitolul nu este un joc, se acordă jumătate din puncte'
setting.documentation_course.name: 'Informații generale'
setting.documentation_course.description: 'Activați froala pentru a adăuga informațiile generale ale cursului în etapa 2 a cursului'
setting.open_course.name: 'Cursuri deschise'
setting.open_course.description: 'Acest lucru permite adăugarea de cursuri deschise la platformă sau formare suplimentară'
setting.client_id.name: 'Id client vimeo'
setting.client_id.description: 'Acesta este identificatorul clientului vimeo'
setting.client_secret.name: 'Client secret vimeo'
setting.client_secret.description: 'Client secret vimeo'
setting.access_token.name: 'Jeton de acces'
setting.access_token.description: 'Jeton de acces'
setting.user_id.name: 'Id utilizator'
setting.user_id.description: 'Id utilizator înregistrat vimeo'
setting.project_id.name: 'Dosar capitol video'
setting.project_id.description: 'Acesta este identificatorul unde sunt găzduite resursele capitolelor video'
setting.project_id_resource_course.name: 'Dosar de resurse materiale (cerere de propuneri)'
setting.project_id_resource_course.description: 'Acesta este identificatorul folderului în care sunt stocate înregistrările video referitoare la materialele de curs și la apel'
setting.project_id_task_course.name: 'Dosar de resurse pentru sarcini'
setting.project_id_task_course.description: 'Este identificatorul folderului în care sunt stocate înregistrările video legate de sarcinile și apelurile cursului'
setting.project_id_video_Quiz.name: 'Dosar de resurse Videoquiz'
setting.project_id_video_Quiz.description: 'Acesta este identificatorul folderului în care sunt găzduite clipurile video legate de jocul videoquiz'
setting.project_id_Roleplay.name: 'Kit de resurse pentru roluri'
setting.project_id_Roleplay.description: 'Identificate pentru resursele de tip video în jocul de rol'
setting.upload_sudomain.name: 'Încărcare în subdomeniu'
setting.upload_sudomain.description: 'Această variabilă este utilizată pentru a încărca videoclipuri și fișiere SCORM, permițând depășirea restricțiilor Cloudflare'
setting.from_email.name: 'De la e-mail'
setting.from_email.description: 'Acesta este originea e-mailurilor trimise de pe platformă'
setting.from_name.name: 'De la numele platformei'
setting.from_name.description: 'Numele platformei indicat în e-mailuri și diplome'
setting.from_cif.name: 'De la CIF'
setting.from_cif.description: 'Numărul de TVA al societății'
setting.email_support.name: 'Asistență prin e-mail'
setting.email_support.description: 'Aceste e-mailuri sunt utilizate pentru a trimite notificări de asistență'
setting.email_support_register.name: 'Primirea e-mailului administratorului'
setting.email_support_register.description: 'Acesta este un e-mail utilizat pentru a primi cererile de înregistrare pe platformă'
setting.news.name: Noutăți
setting.news.description: Noutăți
setting.foro.name: Forum
setting.foro.description: Forum
setting.desafios.name: Provocări
setting.desafios.description: Provocări
setting.secciones.name: Secțiuni
setting.secciones.description: 'Această variabilă vă permite să configurați dacă secțiunile sunt afișate în partea din față'
setting.encuestas.name: Sondaje
setting.encuestas.description: Sondaje
setting.active_cron_exports.name: 'Exporturi cron active'
setting.active_cron_exports.description: 'Exporturi cron active, utilizate în general pentru a exporta date din platformă'
setting.gender_excel.name: 'Genero excel'
setting.gender_excel.description: 'Utilizat pentru a adăuga sexul în excels de export'
setting.code.name: Cod
setting.code.description: 'Afișarea câmpului cod în exportul statisticilor de curs'
setting.finished_chapters.name: 'Capitole finalizate'
setting.finished_chapters.description: 'afișați capitolele finalizate în cursul-stats-export'
setting.zoom_cliente_id.name: 'ID client Zoom'
setting.zoom_cliente_id.description: 'ID client Zoom - necesar pentru a utiliza API Zoom'
setting.zoom_cliente_secret.name: 'Secretul clientului Zoom'
setting.zoom_cliente_secret.description: 'cheia clientului Zoom - necesară pentru a utiliza API-ul de zoom'
setting.zoom_account_id.name: 'ID cont Zoom'
setting.zoom_account_id.description: 'Numărul de cont al clientului Zoom - necesar pentru a utiliza API Zoom'
setting.zoom_email.name: 'Zoom Email'
setting.zoom_email.description: 'e-mail client Zoom - necesar pentru a utiliza API zoom'
setting.clickmeeting_api_key.name: 'Cheia API ClickMeeting'
setting.clickmeeting_api_key.description: 'ID client ClickMeeting - necesar pentru a utiliza API ClickMeeting'
setting.clikmeeting_dirbase.name: 'Directorul de bază ClickMeeting'
setting.clikmeeting_dirbase.description: 'Adresa serverului ClickMeeting'
setting.clikmeeting_events_paralel.name: 'Evenimente secundare ClickMeeting'
setting.clikmeeting_events_paralel.description: 'Numărul de evenimente secundare contractate'
setting.plugnmeet_serverurl.name: 'Url server plugNmeet'
setting.plugnmeet_serverurl.description: 'Adresa serverului plugNmeet'
setting.plugnmeet_api_key.name: 'Cheia API plugNmeet'
setting.plugnmeet_api_key.description: 'ID client plugNmeet'
setting.plugnmeet_secret.name: 'Cheie secretă plugNmeet'
setting.plugnmeet_secret.description: 'Cheie client plugNmeet'
setting.plugnmeet_analyticsurl.name: 'PlugNmeet Analiză URL'
setting.plugnmeet_analyticsurl.description: 'Adresa serverului plugNmeet pentru analiză'
setting.zoom_urlreports.name: 'Url raport zoom'
setting.zoom_urlreports.description: 'Adresa la care sunt stocate rapoartele zoom'
setting.plugnmeet_urlreports.name: 'PlugNmeet raportare url'
setting.plugnmeet_urlreports.description: 'Adresa la care sunt stocate rapoartele plugNmeet'
setting.clickmeeting_urlreports.name: 'URL de raportare ClickMeeting'
setting.clickmeeting_urlreports.description: 'Adresa la care sunt stocate rapoartele ClickMeeting'
setting.library_enabled.name: 'Bibliotecă activată'
setting.library_enabled.description: 'Bibliotecă activată'
setting.library_audio_local.name: 'Audio local al bibliotecii'
setting.library_audio_local.description: 'Audio local al bibliotecii'
setting.library_audio_path.name: 'Calea audio a bibliotecii'
setting.library_audio_path.description: 'Calea audio a bibliotecii'
setting.library_file_path.name: 'Calea fișierului de bibliotecă'
setting.library_file_path.description: 'Calea fișierului de bibliotecă'
setting.library_data_page_size.name: 'Dimensiunea paginii de date de bibliotecă'
setting.library_data_page_size.description: 'Dimensiunea paginii de date de bibliotecă'
setting.library_comments.name: 'Observații din partea bibliotecii'
setting.library_comments.description: 'Observații din partea bibliotecii'
setting.challenge_loops.name: 'Provocări legate de buclă'
setting.challenge_loops.description: 'Provocări legate de buclă'
setting.points_for_win.name: 'Puncte pentru câștig'
setting.points_for_win.description: 'Puncte pentru câștig'
setting.points_for_lose.name: 'Puncte pentru pierdere'
setting.points_for_lose.description: 'Puncte pentru pierdere'
setting.points_fortie.name: 'Puncte pentru egalitate'
setting.points_fortie.description: 'Puncte pentru egalitate'
setting.points_corrects.name: 'Puncte pentru corect'
setting.points_corrects.description: 'Puncte pentru corect'
setting.points_for_left.name: 'Puncte rămase'
setting.points_for_left.description: 'Puncte rămase'
setting.total_duels.name: 'Numărul total de dueluri'
setting.total_duels.description: 'Numărul total de dueluri'
setting.seconds_per_question.name: 'Secunde pe întrebare'
setting.seconds_per_question.description: 'Secunde pe întrebare'
setting.user_dni.name: 'ID utilizator'
setting.user_dni.description: 'Aceasta apare la crearea sau editarea unui utilizator'
setting.edit_code.name: 'Editarea codului'
setting.edit_code.description: 'Este un identificator unic pentru utilizator'
setting.stats_acumulative.name: 'Statistici cumulative'
setting.stats_acumulative.description: 'Acest lucru este valabil în cazul în care doriți ca statisticile să fie cumulative'
setting.maximo_fechas.name: 'Intervalul maxim de date'
setting.maximo_fechas.description: 'Intervalul maxim de date pentru consultări'
setting.maximo_horas.name: 'Numărul maxim de cereri pe oră'
setting.maximo_horas.description: 'Numărul maxim de cereri pe oră'
setting.maximo_dia.name: 'Numărul maxim de cereri pe zi'
setting.maximo_dia.description: 'Numărul maxim de cereri pe zi'
setting.fundae.name: Fundae
setting.fundae.description: 'Dacă acest lucru este activat, atunci când este publicat un apel, utilizatorii trebuie să completeze toate câmpurile necesare din tabelul users_extra_fundae'
setting.margen_entrada.name: 'Marjă de intrare implicită'
setting.margen_entrada.description: 'Marja de intrare implicită, care este utilizată în codul QR'
setting.margen_salida.name: 'Marjă de ieșire implicită'
setting.margen_salida.description: 'Marja de ieșire implicită, care este utilizată în codul QR'
setting.registrar_qr.name: 'Înregistrați-vă cu sesiunea QR'
setting.registrar_qr.description: 'Dacă acest lucru este activat, sesiunile vor fi înregistrate cu QR'
setting.maximo_alumnos.name: 'Numărul maxim de elevi pe grup'
setting.maximo_alumnos.description: 'Numărul maxim de elevi pe grup'
setting.min_score.name: 'Punctaj minim de trecere'
setting.min_score.description: 'Punctaj minim de trecere'
setting.types_action.name: 'Tipuri de acțiuni'
setting.types_action.description: 'Tipuri de acțiuni'
setting.materiales_convocatoria.name: 'Permiterea creării de materiale în cadrul cererilor de propuneri'
setting.materiales_convocatoria.description: 'Permiterea creării de materiale în cadrul cererilor de propuneri'
setting.tareas_convocatoria.name: 'Permiterea creării de sarcini în cadrul unei cereri de propuneri'
setting.tareas_convocatoria.description: 'Permiterea creării de sarcini în cadrul unei cereri de propuneri'
setting.minimo_minutos.name: 'Timpul minim de inactivitate în minute'
setting.minimo_minutos.description: 'Timpul minim de nefuncționare în minute, se aplică utilizatorilor care sunt pe platformă'
setting.timezones.name: 'Zonele orare permise în cererea de propuneri'
setting.timezones.description: 'Fusul orar care poate fi configurat pentru cererea de propuneri'
catalog.1.name: 'Tipuri de capitole'
catalog.1.description: 'Configurarea tipurilor de capitole, care vor fi disponibile pe platformă'
catalog.2.name: 'Tipuri de cursuri'
catalog.2.description: 'Configurarea tipurilor de cursuri, care vor fi disponibile pe platformă'
catalog.3.name: 'Criterii de aprobare'
catalog.3.description: 'Configurarea criteriilor de aprobare, care vor fi disponibile pe platformă'
catalog.4.name: 'Tutor alertă'
catalog.4.description: 'Configurarea alertelor pentru tutori, care vor fi disponibile pe platformă'
catalog.5.name: 'Tipuri de diplome'
catalog.5.description: 'Configurarea tipurilor de diplome, care vor fi disponibile pe platformă'
catalog.6.name: 'Configurație client în apel'
catalog.6.description: 'Configurarea etapelor care urmează să fie afișate în cererea de propuneri'
catalog.7.name: 'Tipuri de monede'
catalog.7.description: 'Configurarea tipurilor de monede, care vor fi disponibile pe platformă'
catalog.8.name: 'Grup de configurații'
catalog.8.description: 'Grup de configurații, care vor fi disponibile pe platformă'
catalog.9.name: Configurații
catalog.9.description: 'Configurații pe grup, care vor fi disponibile pe platformă'
catalog.10.name: Compania
catalog.10.description: 'Companii de utilizatori, care vor fi disponibile pe platformă'
catalog.11.name: 'Categoria profesională'
catalog.11.description: 'Categorii profesionale de utilizatori, care vor fi disponibile pe platformă'
catalog.12.name: 'Centrul de lucru al utilizatorului'
catalog.12.description: 'Locuri de muncă pentru utilizatori, care vor fi disponibile pe platformă'
catalog.13.name: 'Departamentul de lucru al utilizatorului'
catalog.13.description: 'Departamente de lucru pentru utilizatori, care vor fi disponibile pe platformă'
catalog.14.name: 'Nivelul de studiu al utilizatorului'
catalog.14.description: 'Nivelurile de studiu ale utilizatorilor, care vor fi disponibile pe platformă'
catalog.15.name: 'Pași pentru diferitele tipuri de cursuri'
catalog.15.description: 'Configurarea etapelor pentru diferitele tipuri de cursuri, care vor fi disponibile pe platformă'
catalog.16.name: 'Tipuri de săli de clasă virtuale'
catalog.16.description: 'Tipuri de săli de clasă virtuale pentru diferite tipuri de cursuri, care vor fi disponibile pe platformă'
catalog.17.name: 'Tipuri de identificare'
catalog.17.description: 'Tipuri de identificare disponibile pe platformă'
nps_question.text.name: Text
nps_question.text.descripction: 'Dă-ne părerea ta'
setting.help.user.name: 'Includeți pdf de ajutor în meniul utilizatorului'
setting.help.user.description: 'Acest ajutor a fost creat special pentru iberostar'
catalog.18.name: 'Modalități pentru apelurile față în față'
catalog.18.description: 'Aceasta este o nevoie specială pentru Iberostar'
setting.userPolicies_plataforma.name: 'Politica de confidențialitate'
setting.userPolicies_plataforma.description: 'Această variabilă este utilizată pentru a activa un mod pe partea frontală atunci când utilizatorul nu acceptă politica de confidențialitate'
setting.course.tab.person.name: 'Tab persoane detalii curs'
setting.course.tab.stats.name: "Tabelul statistic detalii curs\n"
setting.course.tab.opinions.name: "Tab de opinii detalii curs\n"
setting.documentation.name: Documentație
setting.documentation.description: 'Activați modulul Documentație în meniul lateral'
setting.user_company.name: Companii
setting.user_company.description: 'Activați modulul Companii din meniul lateral'
setting.pages.name: Footer
setting.pages.description: 'Activați footer-ul în campus'
setting.lite_formation.name: 'Grupul de formare în domeniul statisticii generale'
setting.lite_formation.description: 'Grupul de formare în domeniul statisticii generale'
setting.lite_formation.formationHours.name: 'Ore de formare'
setting.lite_formation.formationHours.description: 'Numărul total de ore de formare și numărul mediu de ore de formare pe persoană'
setting.lite_formation.peopleWithCourses.name: 'Persoane cu cursuri'
setting.lite_formation.peopleWithCourses.description: 'Persoane aflate în curs de formare și persoane care au absolvit cel puțin un curs'
setting.lite_formation.courseStartedAndFinished.name: 'Cursuri începute, în curs și finalizate'
setting.lite_formation.courseStartedAndFinished.description: 'Numărul de cursuri începute, în curs și finalizate'
setting.lite_formation.requiredCourses.name: 'Cursuri obligatorii'
setting.lite_formation.requiredCourses.description: 'Cursuri obligatorii atribuite unui apel sau unei căi de acces'
setting.lite_formation.general.name: Generalități
setting.lite_formation.general.description: Generalități
setting.lite_formation.openedCourses.name: 'Cursuri deschise'
setting.lite_formation.openedCourses.description: 'Cursuri voluntare'
setting.lite_formation.educativeStatus.name: 'Nivelul de educație'
setting.lite_formation.educativeStatus.description: 'Stadiul formării pe niveluri de puncte'
setting.lite_formation.gamifiedPills.name: 'Pilule de tip Gamified'
setting.lite_formation.gamifiedPills.description: 'Numărul de capitole gamificate, eșecuri și succese la testele gamificate'
setting.lite_formation.gamifiedTest.name: 'Pastile de testare'
setting.lite_formation.gamifiedTest.description: 'Teste simulate utilizate și succese și eșecuri în funcție de tipul de test'
setting.lite_formation.peoplePerformance.name: 'Performanța oamenilor'
setting.lite_formation.peoplePerformance.description: 'Performanța oamenilor'
setting.lite_formation.coursesByStars.name: 'Cursuri în funcție de punctaj'
setting.lite_formation.coursesByStars.description: 'Clasificarea pe stele a cursurilor'
setting.lite_formation.structureAndHotel.name: 'Apartamente și hoteluri'
setting.lite_formation.structureAndHotel.description: 'Procentaj pe grup'
setting.lite_formation.schoolFinishedAndProgress.name: 'Școală Finalizat și în curs de finalizare'
setting.lite_formation.schoolFinishedAndProgress.description: 'Școala cu cea mai mare participare, cursuri în curs și finalizate'
setting.lite_formation.coursesBySchool.name: 'Cursuri în funcție de școală'
setting.lite_formation.coursesBySchool.description: 'Numărul de cursuri pe categorie'
setting.lite_formation.coursesByDepartment.name: 'Cursuri pe departamente'
setting.lite_formation.coursesByDepartment.description: 'Crearea de cursuri de către departament'
setting.lite_formation.usersMoreActivesByCourses.name: 'Cei mai activi utilizatori pe curs'
setting.lite_formation.usersMoreActivesByCourses.description: 'Cele mai active și cele mai puțin active persoane în cursurile finalizate'
setting.lite_evolution.name: 'Grupul de elaborare a statisticilor generale'
setting.lite_evolution.description: 'Grupul de elaborare a statisticilor generale'
setting.lite_evolution.trainedPerson.name: 'Persoane instruite'
setting.lite_evolution.trainedPerson.description: 'Persoane care au absolvit cel puțin un curs'
setting.lite_evolution.startedCourses.name: 'Cursuri începute'
setting.lite_evolution.startedCourses.description: 'Cursuri începute'
setting.lite_evolution.proccessCourses.name: 'Cursuri în curs de desfășurare'
setting.lite_evolution.proccessCourses.description: 'Cursuri în curs de desfășurare'
setting.lite_evolution.finishedCourses.name: 'Cursuri finalizate'
setting.lite_evolution.finishedCourses.description: 'Cursuri finalizate'
setting.lite_evolution.segmentedHours.name: 'Segmentarea orelor'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Utilizatori noi care au finalizat un curs'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Persoane noi pe platformă care au absolvit cel puțin un curs'
setting.lite_demography.name: 'Grupul Demografie în statistica generală'
setting.lite_demography.description: 'Grupul Demografie în statistica generală'
setting.lite_demography.usersBySexAndAge.name: 'Utilizatori în funcție de sex și vârstă'
setting.lite_demography.usersBySexAndAge.description: 'Utilizatori în funcție de sex și vârstă'
setting.lite_demography.ageDistribution.name: 'Distribuția pe vârste'
setting.lite_demography.ageDistribution.description: 'Distribuția pe vârste'
setting.lite_demography.deviceDistribution.name: 'Distribuția în funcție de dispozitiv'
setting.lite_demography.deviceDistribution.description: 'Distribuția în funcție de dispozitiv'
setting.lite_demography.usersByCountries.name: 'Distribuția pe țări'
setting.lite_demography.usersByCountries.description: 'Distribuția pe țări'
setting.lite_activity.name: 'Statistici generale Grup de activități'
setting.lite_activity.description: 'Statistici generale Grup de activități'
setting.lite_activity.activityInfo.name: 'Informații privind activitatea'
setting.lite_activity.activityInfo.description: 'Persoane active pe portal, persoane înregistrate, persoane care s-au conectat cel puțin o dată în ultimele 30 de zile, persoane dezactivate și persoane care nu s-au conectat niciodată'
setting.lite_activity.accessDays.name: 'Acces pe zile'
setting.lite_activity.accessDays.description: 'Zile de acces'
setting.lite_activity.platformAccessByHours.name: 'Acces în funcție de platformă și de oră'
setting.lite_activity.platformAccessByHours.description: 'Timpii de acces pe platformă în funcție de zi și oră (hartă termică)'
setting.lite_activity.courseStartTime.name: 'Distribuția în funcție de ora de începere a cursurilor'
setting.lite_activity.courseStartTime.description: 'Ora de începere a cursurilor'
setting.lite_activity.courseEndTime.name: 'Distribuția în funcție de orele de absolvire a cursului'
setting.lite_activity.courseEndTime.description: 'Ore de absolvire a cursurilor (hartă termică)'
setting.lite_activity.coursesStartedVsFinished.name: 'Cursuri începute versus cursuri finalizate'
setting.lite_activity.coursesStartedVsFinished.description: 'Cursuri începute vs. cursuri finalizate'
setting.lite_activity.usersMoreActivesByActivity.name: 'Cei mai activi utilizatori'
setting.lite_activity.usersMoreActivesByActivity.description: 'Cele mai active și cele mai puțin active persoane și timpul petrecut de acestea pe platformă'
setting.lite_itinerary.name: 'Itinerarii în grupa Statistici generale'
setting.lite_itinerary.description: 'Itinerarii în grupa Statistici generale'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Inițierea și finalizarea itinerariilor'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Itinerarii începute și finalizate'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Itinerarii finalizate pe țări'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Itinerarii finalizate pe țări'
setting.survey.hide_empty_comment.name: 'Ascundeți opinia cu un comentariu gol'
setting.survey.hide_empty_comment.description: 'Sondaje, ascunde numele atunci când comentariul este gol'
setting.survey.show_only_ratings.name: 'Sondaj, indicați numai numele calificărilor'
setting.survey.show_only_ratings.description: 'Sondajul arată doar numele calificărilor'
app.survey.post_nps.enabled.name: 'Nume anchetă publicație nps activat'
app.survey.post_nps.enabled.description: 'Numele publicației de anchetă a aplicației nps activat'
setting.lite_evolution.segmentedHours.description: 'Ore de lucru'
setting.course.tab.person.description: 'Oameni, detalii despre curs'
setting.course.showDeactivatedCourses.name: 'Tab afișează numele cursurilor dezactivate'
setting.course.showDeactivatedCourses.description: 'Afișează numele cursurilor dezactivate'
catalog.19.name: 'Traduceri administrator'
catalog.19.description: 'Traduceri administrator'
setting.lenguage.platform: 'Administratoresqe Translacie'
setting.module.announcement.name: 'Cerere de candidaturi'
setting.module.announcement.description: 'Activați modulul apeluri în submeniul cursuri'
course.diploma.index: 'Sasto lil'
setting.zip.day_available_until.name: 'Zile disponibile'
setting.zip.day_available_until.description: 'Numărul de zile disponibile înainte ca zip-ul să fie șters automat.'
catalog.20.name: 'Apel pentru câmpuri suplimentare'
course.diploma.filters: 'Activați filtre suplimentare în raportul privind diploma'
setting.lenguage.platform.description: 'Limbi disponibile în panoul administratorului'
translations_admin.title1: 'Formare atribuită'
translations_admin.title2: 'Formare suplimentară'
translations_admin.title3: 'Cursuri alocate'
translations_admin.title4: 'Cursuri voluntare'
setting.course.tab.stats.description: 'Activat activează secțiunea "Statistică" la nivelul detaliat al unui curs.'
setting.course.tab.options.description: 'Activat activează secțiunea "Feedback" la nivelul detaliilor cursului.'
course.diploma.index.description: 'Activat secțiunea "Diplome" este activată la crearea/modificarea unui curs'
setting.use.filter_in_ranking.name: 'Utilizați filtre în clasamentul utilizatorilor'
setting.use.filter_in_ranking.description: 'Vă permite să selectați din meniu filtrele din categoriile cu care un utilizator dorește să fie comparat. Dacă această opțiune este dezactivată, utilizatorul va fi comparat implicit cu toate filtrele disponibile pe platformă'
setting.use.include_only_first_category_name: 'Afișați numai prima categorie a filtrului în clasament'
setting.use.include_only_first_category_description: 'Dacă este activ, se afișează numai prima legătură a utilizatorului. În caz contrar, sunt afișate toate categoriile asociate. De exemplu, dacă categoria este "țară", utilizatorul ar putea fi legat atât de Spania, cât și de Nicaragua.'
setting.email_support_error.name: 'E-mail de asistență pentru erori'
setting.email_support_error.description: 'Mailuri la care vor fi trimise incidentele de pe platformă'
setting.export.task.slot_quantity.name: 'Numărul de sloturi de sarcini per utilizator'
setting.export.task.slot_quantity.description: 'Numărul de sloturi disponibile pentru prelucrarea sarcinilor de export per utilizator.'
setting.export.task.long_running_type_tasks.name: 'Tipuri de sarcini pe termen lung'
setting.export.task.long_running_type_tasks.description: 'Lista tipurilor de sarcini care sunt considerate a fi de lungă durată pentru export.'
setting.export.zip_task.slot_quantity.name: 'Numărul de sloturi pentru sarcini zip per utilizator'
setting.export.zip_task.slot_quantity.description: 'Numărul de sloturi disponibile pentru procesarea sarcinilor de compresie zip per utilizator.'
setting.export.zip_task.long_running_type_tasks.name: 'Tipuri de sarcini zip pe termen lung'
setting.export.zip_task.long_running_type_tasks.description: 'Lista tipurilor de sarcini zip care sunt considerate a fi de lungă durată.'
setting.export.task.user_pending_max_count_task.name: 'Numărul maxim de sarcini în așteptare per utilizator'
setting.export.task.user_pending_max_count_task.description: 'Numărul maxim de sarcini în așteptare pe care un utilizator le poate avea în coadă.'
setting.export.task.timeout.name: 'Termen limită pentru sarcini'
setting.export.task.timeout.description: 'Timpul maxim în secunde înainte ca o sarcină de export să fie considerată expirată.'
setting.export.zip_task.timeout.name: 'Termen limită pentru sarcinile zip'
setting.export.zip_task.timeout.description: 'Timpul maxim în secunde înainte ca o sarcină de compresie zip să fie considerată expirată.'
setting.export.task.timeout_seconds.name: 'Timp de așteptare pentru sarcinile aflate în starea TIMEOUT'
setting.export.task.timeout_seconds.description: 'Timpul maxim în secunde după care o sarcină aflată în starea TIMEOUT nu mai este considerată a fi în curs de execuție.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'Este diploma personalizată pentru Novomatic'
app.announcement.managers.sharing.name: 'Permiterea creării de sarcini în cadrul unei cereri de propuneri'
app.announcement.managers.sharing.description: 'Permiterea creării de sarcini în cadrul unei cereri de propuneri'
