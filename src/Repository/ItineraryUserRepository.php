<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;
use App\Entity\ItineraryUser;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ItineraryUser>
 *
 * @method ItineraryUser|null find($id, $lockMode = null, $lockVersion = null)
 * @method ItineraryUser|null findOneBy(array $criteria, array $orderBy = null)
 * @method ItineraryUser[]    findAll()
 * @method ItineraryUser[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ItineraryUserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ItineraryUser::class);
    }

    public function add(ItineraryUser $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ItineraryUser $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function groupByUser($content)
    {
        $query = $this->createQueryBuilder('iu')
           ->select('u.id')
           ->innerJoin(
               Itinerary::class,
               'i',
               \Doctrine\ORM\Query\Expr\Join::WITH,
               'i.id = iu.itinerary'
           )
           ->innerJoin(
               User::class,
               'u',
               \Doctrine\ORM\Query\Expr\Join::WITH,
               'u.id = iu.user'
           )
           ->groupBy('u.id')
        ;

        if (!empty($content['tags'])) {
            foreach (json_decode($content['tags']) as $key => $item) {
                $param = ':filterTagID' . $key;
                $query
                    ->andWhere('i.tags like ' . $param)
                    ->setParameter($param, '%[' . $item . ']%')
                ;
            }
        }
        if (!empty($content['courseId'])) {
            $query
                 ->innerJoin(
                     ItineraryCourse::class,
                     'ic',
                     \Doctrine\ORM\Query\Expr\Join::WITH,
                     'i.id = ic.itinerary'
                 )
                ->andWhere('ic.course = :course')
                ->setParameter('course', $content['courseId'])
            ;
        }

        return $query
            ->getQuery()
            ->getArrayResult()
        ;
    }
}
