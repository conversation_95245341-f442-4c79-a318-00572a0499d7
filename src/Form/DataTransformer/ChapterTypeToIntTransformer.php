<?php

namespace App\Form\DataTransformer;

use App\Entity\ChapterType;
use App\Repository\ChapterTypeRepository;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Exception\TransformationFailedException;

class ChapterTypeToIntTransformer implements DataTransformerInterface
{
    private ChapterTypeRepository $chapterTypeRepository;


    public function __construct (ChapterTypeRepository $chapterTypeRepository)
    {
        $this->chapterTypeRepository = $chapterTypeRepository;
    }


    /**
     * Transforms an object (ChapterType) to a string (int).
     *
     * @param ChapterType|null $chapterType
     */
    public function transform ($chapterType): string
    {
        if (null === $chapterType)
        {
            return '';
        }

        return $chapterType->getId();
    }


    /**
     * Transforms a string (number) to an object (issue).
     *
     * @param string $chapterTypeId
     * @throws TransformationFailedException if object (issue) is not found.
     */
    public function reverseTransform ($chapterTypeId): ?ChapterType
    {
        if (!$chapterTypeId)
        {
            return null;
        }

        $chapterType = $this->chapterTypeRepository->find($chapterTypeId);

        if (null === $chapterType)
        {
            // causes a validation error
            // this message is not shown to the user
            // see the invalid_message option
            throw new TransformationFailedException(sprintf(
                'ChapterType with id "%s" does not exist!',
                $chapterTypeId
            ));
        }

        return $chapterType;
    }
}
