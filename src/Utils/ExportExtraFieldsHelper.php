<?php

declare(strict_types=1);

namespace App\Utils;

class ExportExtraFieldsHelper
{
    public static function getExportHeaders(?array $settingExtraFields, string $locale): array
    {
        $headers = [];

        if (null === $settingExtraFields) {
            return $headers;
        }

        foreach ($settingExtraFields as $extraField) {
            $translatedLabel = $extraField['label']['default'];

            foreach ($extraField['label']['translations'] as $translation) {
                if ($translation['language'] === $locale) {
                    $translatedLabel = $translation['value'];
                    break;
                }
            }

            $headers[$extraField['name']] = $translatedLabel;
        }

        return $headers;
    }

    /**
     * @throws \UnexpectedValueException
     */
    public static function getExportExtraFieldsValues(
        ?array $settingExtraFields,
        array $userExtraFields,
        array $userExtraFieldTranslatedHeaders,
        string $locale
    ): array {
        $values = [];

        if (null === $settingExtraFields) {
            return $values;
        }

        foreach ($settingExtraFields as $extraField) {
            $settingUserExtraField = $userExtraFields[$extraField['name']] ?? null;

            if ($settingUserExtraField) {
                if ('select' === $extraField['type']) {
                    if (!filter_var($settingUserExtraField, FILTER_VALIDATE_INT)) {
                        throw new \UnexpectedValueException('Invalid value for select field. Expected integer.');
                    }

                    $valueOption = array_filter($extraField['options'], fn ($option) => $option['value'] === $settingUserExtraField);
                    $valueOption = reset($valueOption);
                    $translatedValue = $valueOption['name']['default'] ?? '';
                    foreach ($valueOption['name']['translations'] as $translation) {
                        if ($translation['language'] === $locale) {
                            $translatedValue = $translation['value'];
                            break;
                        }
                    }

                    $values[$userExtraFieldTranslatedHeaders[$extraField['name']]] = $translatedValue;
                    continue;
                }

                $values[$userExtraFieldTranslatedHeaders[$extraField['name']]] = $settingUserExtraField;
            } else {
                $values[$userExtraFieldTranslatedHeaders[$extraField['name']]] = '';
            }
        }

        return $values;
    }
}
