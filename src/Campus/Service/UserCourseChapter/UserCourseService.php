<?php

declare(strict_types=1);

namespace App\Campus\Service\UserCourseChapter;

use App\Campus\Games\GameService;
use App\Campus\Service\Base\BaseService;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Symfony\Component\Security\Core\Security;

class UserCourseService extends BaseService
{
    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security,
        private readonly GameService $chapterTypeService
    ) {
        parent::__construct($em, $settings, $security);
    }

    /**
     * @throws ORMException
     */
    public function setFinishUserCourse(UserCourse $userCourse): void
    {
        $course = $userCourse->getCourse();

        $chapters = [];
        foreach ($course->getChapters() as $chapter) {
            $hasSeason = null !== $chapter->getSeason();
            $isChapterTypeActive = $chapter->getType()->isActive();
            $isChapterActive = $chapter->isIsActive();
            $hasContentCompleted = $chapter->hasContentCompleted();

            if ($hasSeason && $isChapterTypeActive && $isChapterActive && $hasContentCompleted) {
                $chapters[$chapter->getId()] = $chapter;
            }
        }

        $chapterFinished = 0;
        foreach ($userCourse->getChapters() as $userCourseChapter) {
            $chapter = $chapters[$userCourseChapter->getChapter()->getId()] ?? null;
            if (null === $chapter) {
                continue;
            }

            if (!\is_null($userCourseChapter->getFinishedAt()) && $chapter->isIsActive()) {
                ++$chapterFinished;
            }
        }

        if (\count($chapters) <= $chapterFinished) {
            $userCourse->setFinishedAt(new \DateTime());
            if (!$this->settings->get('app.opinions.platform')) {
                $this->calculatePointsUserCourse($userCourse);
            }

            $this->em->persist($userCourse);
        }
    }

    /**
     * @throws ORMException
     */
    public function calculatePointsUserCourse(UserCourse $userCourse): void
    {
        $points = 0;
        $course = $userCourse->getCourse();

        if ($userCourse->getPoints() > 0) {
            $userCourse->setPoints(0);
        }

        foreach ($userCourse->getChapters() as $userCourseChapter) {
            $chapter = $userCourseChapter->getChapter();
            if (
                null === $chapter
                || !$chapter->isIsActive()
                || !$chapter->getType()->isActive()
                || !$chapter->hasContentCompleted()
            ) {
                continue;
            }

            if (!$userCourseChapter->getPoints()) {
                $userCourseChapter->setPoints(0);
            }
            $points += $userCourseChapter->getPoints() ?? 0;
        }

        if ($course->hasChapterContent()) {
            $points += $course->contentChaptersMaxPoints();
        }

        $userCourse->setPoints($points);

        $user = $userCourse->getUser();
        $user->setPoints($user->getPoints() + \intval($points));
        $this->em->persist($user);

        $this->em->persist($userCourse);
    }

    public function calculatePoints(UserCourseChapter $userCourseChapter): float|int|null
    {
        $chapter = $userCourseChapter->getChapter();
        $data = $userCourseChapter->getData();
        $gamePercentage = $this->chapterTypeService->calculateGamePoints($chapter, $data);
        $maxPoints = $this->getMaxPoints($userCourseChapter) > 0 ? $this->getMaxPoints($userCourseChapter) : 100;

        $value = \is_null($gamePercentage) ? $gamePercentage : \intval(round($maxPoints * $gamePercentage));

        if (0 == $value && $gamePercentage > 0) {
            return round($maxPoints * 0.5);
        }

        return $value;
    }

    public function getPointByAttempInGame(UserCourseChapter $userCourseChapter, $data): ?float
    {
        $chapter = $userCourseChapter->getChapter();
        $finalData = $userCourseChapter->getData();
        $finalData['answers'] = $data['answers'];
        $gamePercentage = $this->chapterTypeService->calculateGamePoints($chapter, $finalData);
        $maxPoints = $this->getMaxPoints($userCourseChapter) > 0 ? $this->getMaxPoints($userCourseChapter) : 100;
        $value = \is_null($gamePercentage) ? $gamePercentage : round($maxPoints * $gamePercentage);

        if (0 == $value && $gamePercentage > 0) {
            return round($maxPoints * 0.5);
        }

        return $value;
    }

    public function getMaxPoints(UserCourseChapter $userCourseChapter): float|int|null
    {
        $chapter = $userCourseChapter->getChapter();
        $course = $userCourseChapter->getUserCourse()->getCourse();

        if ($chapter->getType()->isGame()) {
            return $course->gamesMaxPoints();
        } else {
            return $course->contentChaptersMaxPoints();
        }
    }
}
