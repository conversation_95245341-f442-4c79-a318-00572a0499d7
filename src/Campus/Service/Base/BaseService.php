<?php

namespace App\Campus\Service\Base;

use App\Service\SettingsService;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

abstract class BaseService
{
    protected EntityManager $em;
    protected SettingsService $settings;
    protected Security  $security;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security

    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->security = $security;
    }

   

    public function getUser()
    {
        return $this->security->getUser();
    }
}
