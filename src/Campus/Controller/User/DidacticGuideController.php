<?php

declare(strict_types=1);

namespace App\Campus\Controller\User;

use App\Campus\Controller\Base\BaseController;
use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Service\Api\AnnouncementNewsService;
use App\Service\Api\FormationUserService;
use App\Service\Api\ItineraryUserService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\TokenExtractor\TokenExtractorInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class DidacticGuideController extends BaseController
{
    private $itineraryUserService;
    private $formationUserService;
    private $announcementNewsService;

    private TokenExtractorInterface $tokenExtractor;
    private JWTTokenManagerInterface $jwtManager;

    /**
     * ApiController constructor.
     */
    public function __construct(
        TranslatorInterface $translator,
        ItineraryUserService $itineraryService,
        FormationUserService $formationUserService,
        EntityManagerInterface $em,
        TokenExtractorInterface $tokenExtractor,
        JWTTokenManagerInterface $jwtManager,
        AnnouncementNewsService $announcementNewsService,
        SettingsService $settings
    ) {
        parent::__construct($settings, $em, $translator);

        $this->itineraryUserService = $itineraryService;
        $this->formationUserService = $formationUserService;
        $this->em = $em;
        $this->tokenExtractor = $tokenExtractor;
        $this->jwtManager = $jwtManager;
        $this->announcementNewsService = $announcementNewsService;
    }

    /**
     * @Rest\Post("/guide-didactic/{id}", name="download_guide_didactic")
     */
    public function guideDidacticAnnouncement(int $id, Request $request): Response
    {
        try {
            $isDownload = json_decode($request->getContent(), true)['isDownload'] ?? false;
            if (!($announcement = $this->em->getRepository(Announcement::class)->find($id))) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Convocatoria no encontrada',
                ]);
            }

            $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
                'announcement' => $announcement,
                'user' => $this->getUser(),
            ]);

            if (!$announcementUser) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'El usuario no encontrado.',
                ]);
            }

            $this->formationUserService->updateDidacticGuideStatus($announcementUser, $isDownload);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'Success',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }
}
