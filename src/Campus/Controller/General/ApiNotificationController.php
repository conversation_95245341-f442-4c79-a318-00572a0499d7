<?php

namespace App\Campus\Controller\General;

use App\Entity\Message;
use App\Entity\MessageAttachment;
use App\Entity\Notification;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\MessageRepository;
use App\Repository\NotificationRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Ramsey\Collection\Set;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Swagger\Annotations as SWG;
use Vich\UploaderBundle\Handler\DownloadHandler;
use Symfony\Contracts\Translation\TranslatorInterface;


/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiNotificationController extends ApiBaseController
{

    private $em;
    private $notificationRepository;


    /**
     * ApiController constructor.
     *
     * @param LoggerInterface $logger
     * @param UserRepository $userRepository
     * @param CourseRepository $courseRepository
     * @param EntityManagerInterface $em
     * @param NotificationRepository $notificationRepository
     */
    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        EntityManagerInterface $em,
        NotificationRepository $notificationRepository,
        TranslatorInterface $translator,
        SettingsService $settingsService
    ) {

        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
        $this->em = $em;
        $this->notificationRepository = $notificationRepository;
    }


    /**
     * @Rest\Get("/notifications", name="api_notifications_get")
     *
     * @return Response
     */
    public function getNotifications()
    {
        $notifications = $this->notificationRepository->findBy(['user' => $this->getUser()], ['createdAt' => 'DESC']);

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => $notifications,
        ];

        return $this->sendResponse($response, ['groups' => ['list']]);
    }


    /**
     * @Rest\Post("/notifications/read", name="api_notifications_read")
     *
     * @param Notification $notification
     * @return Response
     */
    public function markAsRead(Request $request)
    {
        $content = json_decode($request->getContent(), true);

        $notificationId = $content['id'];
        $notification = $this->notificationRepository->find($notificationId);

        $code  = Response::HTTP_OK;
        $error = false;
        $message = '';

        if (!$notification) {
            $code    = Response::HTTP_NOT_FOUND;
            $error   = true;
            $message = 'Notification not found';
        } else if ($notification->getUser() !== $this->getUser()) {
            $code    = Response::HTTP_UNAUTHORIZED;
            $error   = true;
            $message = 'Access not allowed';
        } else {
            $notification->setReadAt(new \DateTime());
            $this->em->persist($notification);
            $this->em->flush();
        }

        $response = [
            'status' => $code,
            'error'  => $error,
            'data'   => $code !== Response::HTTP_OK ? $message : $notification,
        ];

        return $this->sendResponse($response, ['groups' => ['detail']]);
    }
}
