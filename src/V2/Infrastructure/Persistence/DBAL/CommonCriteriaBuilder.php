<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\DBAL;

use App\V2\Domain\Shared\Criteria\Criteria;
use App\V2\Domain\Shared\Criteria\CriteriaId;
use App\V2\Domain\Shared\Criteria\SortDirection;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Query\QueryBuilder;

class CommonCriteriaBuilder
{
    public static function filterByCommonCriteria(
        Criteria $criteria,
        QueryBuilder $queryBuilder,
        bool $onlyFilter = false
    ): void {
        if ($criteria instanceof CriteriaId) {
            self::filterByCommonCriteriaId($criteria, $queryBuilder);
        }

        if ($onlyFilter) {
            return;
        }

        if ($criteria->getPagination()) {
            $queryBuilder->setFirstResult($criteria->getPagination()->offset());
            $queryBuilder->setMaxResults($criteria->getPagination()->limit());
        }

        if (null !== $criteria->getSortBy()) {
            foreach ($criteria->getSortBy()->all() as $sortBy) {
                $queryBuilder->addOrderBy(
                    't.' . $sortBy->getField()->value(),
                    self::fromSortDirectionToSQL($sortBy->getDirection())
                );
            }
        }
    }

    private static function filterByCommonCriteriaId(CriteriaId $criteria, QueryBuilder $queryBuilder): void
    {
        if (null !== $criteria->getId()) {
            $queryBuilder->setParameter('id', $criteria->getId());
            $queryBuilder->andWhere('t.id = :id');
        }

        if (null !== $criteria->getIds() && !$criteria->getIds()->isEmpty()) {
            $queryBuilder->setParameter('ids', $criteria->getIds()->all(), ArrayParameterType::STRING);
            $queryBuilder->andWhere('t.id in (:ids)');
        }
    }

    private static function fromSortDirectionToSQL(SortDirection $sortDirection): string
    {
        return match ($sortDirection) {
            SortDirection::ASC => 'ASC',
            SortDirection::DESC => 'DESC',
        };
    }
}
