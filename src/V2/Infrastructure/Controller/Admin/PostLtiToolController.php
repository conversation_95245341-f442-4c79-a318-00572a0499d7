<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PostLtiToolCommand;
use App\V2\Domain\Shared\Url\InvalidUrlException;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiToolValidator;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostLtiToolController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws InvalidUrlException
     * @throws InvalidUuidException
     */
    public function __invoke(Request $request, string $registrationId): Response
    {
        UuidValidator::validateUuid($registrationId);

        $data = json_decode($request->getContent(), true);

        LtiToolValidator::validatePostLtiTool($data);

        $this->execute(
            new PostLtiToolCommand(
                registrationId: new Uuid($registrationId),
                name: $data['name'],
                audience: $data['audience'],
                oidcInitiationUrl: new Url($data['oidc_initiation_url']),
                launchUrl: new Url($data['launch_url']),
                deepLinkingUrl: new Url($data['deep_linking_url']),
                jwksUrl: new Url($data['jwks_url']),
            )
        );

        return new JsonResponse(
            data: [],
            status: Response::HTTP_CREATED
        );
    }
}
