<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Announcement\Manager;

use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepositoryException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;

class DBALAnnouncementManagerRepository implements AnnouncementManagerRepository
{
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $em,
        private readonly string $announcementManagerTableName,
    ) {
        $this->connection = $em->getConnection();
    }

    #[\Override]
    public function insert(AnnouncementManager $announcementManager): void
    {
        try {
            $this->findOneBy(
                AnnouncementManagerCriteria::createEmpty()
                    ->filterByUserId($announcementManager->getUserId()->value())
                    ->filterByAnnouncementId($announcementManager->getAnnouncementId()->value())
            );

            throw AnnouncementManagerRepositoryException::duplicateAnnouncementManager($announcementManager);
        } catch (AnnouncementManagerNotFoundException) {
        }
        try {
            $this->connection
                ->insert(
                    table: $this->announcementManagerTableName,
                    data: $this->fromAnnouncementManagerToArray($announcementManager),
                );
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(AnnouncementManagerCriteria $criteria): AnnouncementManager
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new AnnouncementManagerNotFoundException();
            }

            return $this->fromArrayToAnnouncementManager($result);
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(AnnouncementManagerCriteria $criteria): AnnouncementManagerCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new AnnouncementManagerCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToAnnouncementManager($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(AnnouncementManager $announcementManager): void
    {
        try {
            $this->connection
                ->delete(
                    table: $this->announcementManagerTableName,
                    criteria: [
                        'manager_id' => $announcementManager->getUserId()->value(),
                        'announcement_id' => $announcementManager->getAnnouncementId()->value(),
                    ]
                );
        } catch (DBALException $e) {
            throw AnnouncementManagerRepositoryException::fromPrevious($e);
        }
    }

    private function fromAnnouncementManagerToArray(AnnouncementManager $announcementManager): array
    {
        return [
            'manager_id' => $announcementManager->getUserId(),
            'announcement_id' => $announcementManager->getAnnouncementId(),
        ];
    }

    private function fromArrayToAnnouncementManager(array $values): AnnouncementManager
    {
        return new AnnouncementManager(
            userId: new Id($values['manager_id']),
            announcementId: new Id($values['announcement_id']),
        );
    }

    private function getQueryBuilderByCriteria(AnnouncementManagerCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->announcementManagerTableName, 't');

        if (null !== $criteria->getUserId()) {
            $queryBuilder->andWhere('t.manager_id = :userId')
                ->setParameter('userId', $criteria->getUserId());
        }

        if (null !== $criteria->getAnnouncementId()) {
            $queryBuilder->andWhere('t.announcement_id = :announcementId')
                ->setParameter('announcementId', $criteria->getAnnouncementId());
        }

        return $queryBuilder;
    }
}
