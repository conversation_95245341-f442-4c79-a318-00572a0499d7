<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Uuid;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;

class UuidValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateUuid(string $uuid): void
    {
        $constraints = new Constraints\Collection([
            'uuid' => [
                new Constraints\NotBlank(),
                new Constraints\Type('string'),
                new Constraints\Uuid(versions: [Constraints\Uuid::V4_RANDOM]),
            ],
        ]);

        parent::validate(['uuid' => $uuid], $constraints);
    }
}
