<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin\LTI;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints;

class LtiPlatformValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validatePostLtiPlatform(array $data): void
    {
        $constraints = [
            new Constraints\NotBlank(message: 'Body cannot be empty'),
            new Constraints\Collection([
                'name' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                ],
                'audience' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                ],
                'oidc_authentication_url' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                    new Constraints\Url(),
                ],
                'oauth2_access_token_url' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                    new Constraints\Url(),
                ],
                'jwks_url' => [
                    new Constraints\NotBlank(),
                    new Constraints\Type('string'),
                    new Constraints\Url(),
                ],
            ])];

        parent::validate($data, $constraints);
    }
}
