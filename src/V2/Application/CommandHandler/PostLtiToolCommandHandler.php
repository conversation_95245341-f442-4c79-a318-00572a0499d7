<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\PostLtiToolCommand;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiToolNotFoundException;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\LTI\LtiTool;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class PostLtiToolCommandHandler
{
    public function __construct(
        private LtiRegistrationRepository $ltiRegistrationRepository,
        private LtiToolRepository $ltiToolRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws LtiRegistrationNotFoundException
     * @throws CriteriaException
     */
    public function handle(PostLtiToolCommand $command): void
    {
        $registration = $this->ltiRegistrationRepository->findOneBy(
            LtiRegistrationCriteria::createById($command->getRegistrationId()),
        );

        try {
            $id = $this->ltiToolRepository->findOneBy(
                LtiToolCriteria::createEmpty()->filterByRegistrationId(
                    $registration->getId()
                )
            )->getId();
        } catch (LtiToolNotFoundException) {
            $id = $this->uuidGenerator->generate();
        }

        $tool = new LtiTool(
            id: $id,
            registrationId: $registration->getId(),
            name: $command->getName(),
            audience: $command->getAudience(),
            oidcInitiationUrl: $command->getOidcInitiationUrl(),
            launchUrl: $command->getLaunchUrl(),
            deepLinkingUrl: $command->getDeepLinkingUrl(),
            jwksUrl: $command->getJwksUrl(),
        );

        $this->ltiToolRepository->put($tool);
    }
}
