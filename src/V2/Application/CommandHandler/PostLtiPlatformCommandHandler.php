<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\PostLtiPlatformCommand;
use App\V2\Domain\LTI\Exceptions\LtiPlatformNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\LTI\LtiPlatformCriteria;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class PostLtiPlatformCommandHandler
{
    public function __construct(
        private LtiRegistrationRepository $ltiRegistrationRepository,
        private LtiPlatformRepository $ltiPlatformRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws LtiRegistrationNotFoundException
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function handle(PostLtiPlatformCommand $command): void
    {
        $registration = $this->ltiRegistrationRepository->findOneBy(
            LtiRegistrationCriteria::createById($command->getRegistrationId()),
        );

        try {
            $id = $this->ltiPlatformRepository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterByRegistrationId($registration->getId())
            )->getId();
        } catch (LtiPlatformNotFoundException) {
            $id = $this->uuidGenerator->generate();
        }

        $platform = new LtiPlatform(
            id: $id,
            registrationId: $registration->getId(),
            name: $command->getName(),
            audience: $command->getAudience(),
            oidcAuthenticationUrl: $command->getOidcAuthenticationUrl(),
            oauth2AccessTokenUrl: $command->getOauth2AccessTokenUrl(),
            jwksUrl: $command->getJwksUrl(),
        );

        $this->ltiPlatformRepository->put($platform);
    }
}
