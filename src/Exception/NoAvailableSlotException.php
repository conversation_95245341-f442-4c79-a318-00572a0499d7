<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpFoundation\Response;

class NoAvailableSlotException extends \RuntimeException
{
    private int $statusCode;

    public function __construct(string $message, int $statusCode = Response::HTTP_SERVICE_UNAVAILABLE)
    {
        parent::__construct($message);
        $this->statusCode = $statusCode;
    }

    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    // Basic exception for when no execution slots are available
}
