<?php

declare(strict_types=1);

namespace App\Service\Diploma\Strategy;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\TypeCourse;
use App\Entity\TypeIdentification;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserFieldsFundae;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\DateFormatter\DateFormatterService;
use App\Service\SettingsService;
use App\V2\Application\Helper\Time\HourFormatterHelper;
use App\V2\Domain\Diploma\Helper\DateHelper;
use Doctrine\ORM\EntityManagerInterface;

class DiplomaBase implements DiplomaInterface
{
    public function __construct(
        protected EntityManagerInterface $em,
        protected SettingsService $settings,
        protected AnnouncementConfigurationsService $announcementConfiguration,
        protected DateFormatterService $dateFormatter,
    ) {
    }

    public function getContentCourseDiploma($request, $user): array
    {
        $course = $this->em->getRepository(Course::class)->find($request['idCourse']);
        $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy(['user' => $user, 'course' => $course]);

        // Determine which date to use
        $dateToUse = DateHelper::getDateToUse(
            $request['date'] ?? null,
            $userCourse?->getFinishedAt()
        );

        // Use DateFormatterService for consistent date formatting
        $userLocale = $this->localeDefaultUser($user);
        $formatDate = $this->dateFormatter->formatDate($dateToUse, 'medium', $userLocale);
        $showDuration = $this->shouldShowCourseDuration($course);

        return [
            'user' => $user,
            'course' => $course,
            'date' => $formatDate,
            'center' => !empty($request['center']) ? $request['center'] : '',
            'department' => !empty($request['department']) ? $request['department'] : '',
            'locale' => $userLocale,
            'currentDate' => $this->dateFormatter->formatDate(new \DateTime(), 'medium', $userLocale),
            'showDuration' => $showDuration,
            'courseDurationHours' => $showDuration ? HourFormatterHelper::minutesToHours($course?->getDuration()) : null,
        ];
    }

    public function getContentAnnouncementDiploma(Announcement $announcement, User $user): array
    {
        $center = $user->getExtra() && $user->getExtra()->getCenter() ? $user->getExtra()->getCenter()->getName() : '--';
        $department = $user->getExtra() && $user->getExtra()->getDepartment() ? $user->getExtra()->getDepartment()->getName() : '--';
        $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy(['user' => $user->getId(), 'announcement' => $announcement]);

        $date = $userCourse ? $userCourse->getFinishedAt() : $announcement->getFinishAt();
        $formativesActionType = $this->settings->get('app.subsidizer.formativeActionTypes');

        $announcementGroupInfo = $this->fetchInformationAnnouncementGroup($announcement, $user);

        // Use DateFormatterService for consistent date formatting
        $userLocale = $this->localeDefaultUser($user);
        $dateToUse = $date ?: new \DateTime();
        $formatDate = $this->dateFormatter->formatDate($dateToUse, 'medium', $userLocale);

        return array_merge(
            [
                'user' => $user,
                'course' => $announcement->getCourse(),
                'date' => $formatDate,
                'center' => $center,
                'department' => $department,
                'locale' => $userLocale,
                'type' => $formativesActionType['INTERN'],
                'announcement' => $announcement,
                'hasDni' => $this->announcementConfiguration->hasDniInDiploma($announcement) ?? false,
                'dni' => $this->getIdentificationUserForCertificate($user),
                'typeCourseName' => $this->getTypeCourseName($announcement->getCourse()->getTypeCourse(), $user->getLocale()),
                'mainIdentification' => $this->em->getRepository(TypeIdentification::class)->getMainIdentificationForThePlatform($user->getLocale()),
                'currentDay' => (new \DateTime())->format('d'),
                // Formatted dates for templates
                'startDate' => $this->dateFormatter->formatDate($announcement->getStartAt(), 'medium', $userLocale),
                'finishDate' => $this->dateFormatter->formatDate($announcement->getFinishAt(), 'medium', $userLocale),
                'currentDate' => $this->dateFormatter->formatDate(new \DateTime(), 'medium', $userLocale),
                // Special formats for Hobetuz templates (euskera format)
                'startYear' => $announcement->getStartAt()->format('Y'),
                'startMonth' => $announcement->getStartAt()->format('m'),
                'startDay' => $announcement->getStartAt()->format('d'),
                'finishYear' => $announcement->getFinishAt()->format('Y'),
                'finishMonth' => $announcement->getFinishAt()->format('m'),
                'finishDay' => $announcement->getFinishAt()->format('d'),
                'currentYear' => (new \DateTime())->format('Y'),
                // Duration variables for template compatibility (announcement diplomas don't show course duration)
                'showDuration' => false,
                'courseDurationHours' => null,
            ],
            $announcementGroupInfo
        );
    }

    private function getTypeCourseName(TypeCourse $typeCourse, $locale): ?string
    {
        $typeCourseName = null;
        $typeCourse = $this->em->getRepository(TypeCourse::class)->find($typeCourse->getId());
        if ($locale) {
            $typeCourseTranslation = $typeCourse->translate($locale);
            $typeCourseName = $typeCourseTranslation->getName();
        }

        return $typeCourseName ?? $typeCourse->getName();
    }

    private function getIdentificationUserForCertificate($user): ?string
    {
        $userExtraFundae = $this->em->getRepository(UserFieldsFundae::class)->findOneBy(['user' => $user->getId()]);

        return $userExtraFundae ? $userExtraFundae->getDni() : $user->getRegisterKey();
    }

    private function fetchInformationAnnouncementGroup($announcement, $user): array
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['user' => $user->getId(), 'announcement' => $announcement]);

        if ($announcementUser) {
            $group = $announcementUser->getAnnouncementGroup();
        } else {
            $group = null;
        }

        $code = $group ? $group->getCode() : '--';
        $cif = $group ? $group->getCompanyCif() : '--';
        $company = $group ? $group->getCompanyProfile() : $this->settings->get('app.fromName');

        return [
            'codeGroup' => $code,
            'enterpriseCIF' => $cif,
            'enterprise' => $company,
        ];
    }

    protected function localeDefaultUser(?User $user): string
    {
        if ($user && $user->getLocaleCampus()) {
            return $user->getLocaleCampus();
        }

        $defaultLanguage = $this->settings->get('app.defaultLanguage');

        return $defaultLanguage ?? 'es';
    }

    protected function shouldShowCourseDuration(Course $course): bool
    {
        $diplomaConfig = $course->getDiplomaConfig();

        return $diplomaConfig ? $diplomaConfig->showDuration() : false;
    }
}
