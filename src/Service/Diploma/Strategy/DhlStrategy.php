<?php

declare(strict_types=1);

namespace App\Service\Diploma\Strategy;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\Filter;
use App\Entity\User;
use App\Entity\UserCourse;
use App\V2\Application\Helper\Time\HourFormatterHelper;
use App\V2\Domain\Diploma\Helper\DateHelper;

class DhlStrategy extends DiplomaBase
{
    public function getContentCourseDiploma($request, $user): array
    {
        $course = $this->em->getRepository(Course::class)->find($request['idCourse']);

        $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy([
            'course' => $request['idCourse'],
            'user' => $user,
        ]);

        // Determine which date to use
        $dateToUse = DateHelper::getDateToUse(
            $request['date'] ?? null,
            $userCourse?->getFinishedAt()
        );

        $filters = $this->em->getRepository(Filter::class)->fetchFiltersUsersAll(
            $user->getId(),
            3
        );

        $filtersPlace = $this->em->getRepository(Filter::class)->fetchFiltersUsersAll(
            $user->getId(),
            1
        );

        // Use DateFormatterService for consistent date formatting
        $userLocale = $this->localeDefaultUser($user);
        $formatDate = $this->dateFormatter->formatDate($dateToUse, 'medium', $userLocale);
        $monthName = $this->dateFormatter->getMonthName((int) $dateToUse->format('n'), $userLocale);
        $showDuration = $this->shouldShowCourseDuration($course);

        return [
            'user' => $user,
            'course' => $course,
            'date' => $formatDate,
            'center' => $request['center'],
            'department' => $request['department'],
            'locale' => $userLocale,
            'finishedAt' => $dateToUse,
            'filters' => $filters ? $filters : [],
            'month' => $monthName,
            'place' => $filtersPlace ? $filtersPlace : [],
            'showDuration' => $showDuration,
            'courseDurationHours' => $showDuration ? HourFormatterHelper::minutesToHours($course?->getDuration()) : null,
        ];
    }

    public function getContentAnnouncementDiploma(Announcement $announcement, User $user): array
    {
        return [];
    }
}
