<?php

declare(strict_types=1);

namespace App\Service\Diploma\Strategy;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserCourse;
use App\V2\Application\Helper\Time\HourFormatterHelper;

class NovomaticStrategy extends DiplomaBase
{
    /**
     * Main method to generate the diploma content for courses
     * Includes the necessary fields for the Novomatic diploma.
     * Uses the valued_at date from UserCourse for the diploma date.
     */
    public function getContentCourseDiploma($request, $user): array
    {
        $course = $this->em->getRepository(Course::class)->find($request['idCourse']);

        // Get the UserCourse to find the valued_at date
        $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy([
            'user' => $user->getId(),
            'course' => $course->getId(),
        ]);

        $dateToUse = null;
        $formatDate = null;

        if ($userCourse && $userCourse->getValuedAt()) {
            $dateToUse = $userCourse->getValuedAt();
            // Use DateFormatterService for proper internationalization
            $userLocale = $this->localeDefaultUser($user);
            $formatDate = $this->dateFormatter->formatDate($dateToUse, 'medium', $userLocale);
        }

        $showDuration = $this->shouldShowCourseDuration($course);

        // We include the necessary fields for the Novomatic diploma
        return [
            // Basic fields needed for the system
            'user' => $user,
            'course' => $course,
            'date' => $formatDate,
            'locale' => $this->localeDefaultUser($user),
            'showDuration' => $showDuration,
            'courseDurationHours' => $showDuration ? HourFormatterHelper::minutesToHours($course?->getDuration()) : null,

            // DIPLOMA FIELDS
            'userName' => $user->getFirstName() . ' ' . $user->getLastName(),
            'courseName' => $course->getName(),
            'diplomaDate' => $formatDate,
        ];
    }

    /**
     * Method to generate diploma content for announcements
     * Uses the same method as for direct courses, but adds announcement-specific information.
     * Uses the date_approved from AnnouncementUser for the diploma date.
     */
    public function getContentAnnouncementDiploma(Announcement $announcement, User $user): array
    {
        // Get the AnnouncementUser to find the date_approved
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'user' => $user->getId(),
            'announcement' => $announcement,
        ]);

        // Determine which date to use
        $dateToUse = null;

        if ($announcementUser && $announcementUser->getDateApproved()) {
            $dateToUse = $announcementUser->getDateApproved();
        } else {
            // Fallback to current date if no date_approved is found
            $dateToUse = new \DateTime();
        }

        // Use DateFormatterService for proper internationalization
        $userLocale = $this->localeDefaultUser($user);
        $finishDate = $this->dateFormatter->formatDate($dateToUse, 'medium', $userLocale);

        // Create a simulated request with announcement data
        $request = [
            'idCourse' => $announcement->getCourse()->getId(),
            'date' => $finishDate,
        ];

        // Get the base content of the diploma
        $content = $this->getContentCourseDiploma($request, $user);

        // Override the diploma date with the one we determined
        $content['diplomaDate'] = $finishDate;

        // Add announcement-specific data
        $content['announcement'] = $announcement;

        return $content;
    }
}
