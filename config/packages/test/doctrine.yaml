doctrine:
  dbal:
    connections:
      default:
        url: '%env(resolve:DATABASE_URL)%'
      sqlite:
        driver: pdo_sqlite
        memory: true
    default_connection: default
  orm:
    default_entity_manager: default
    auto_generate_proxy_classes: true
    entity_managers:
      default:
        auto_mapping: true
        connection: default
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        mappings:
          App:
            is_bundle: false
            type: annotation
            dir: '%kernel.project_dir%/src/Entity'
            prefix: 'App\Entity'
            alias: App
          GesdinetJWTRefreshTokenBundle:
            type: xml
            dir: 'Resources/config/doctrine'
      sqlite:
        connection: sqlite
        mappings:
          App:
            is_bundle: false
            type: annotation
            dir: '%kernel.project_dir%/src/Entity'
            prefix: 'App\Entity'
            alias: App
          GesdinetJWTRefreshTokenBundle:
            type: xml
            dir: 'Resources/config/doctrine'
