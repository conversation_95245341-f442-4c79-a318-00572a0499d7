<template>
  <div class="UserFormView">
    <LayoutPageTitle
      v-if="title"
      :links="links"
      :name="$t(title)"
    />
    <LayoutPageActions>
      <BaseButton @click="submitAndReset">
        {{ $t(userData.id ? 'COURSE.UPDATE_AND_ADD_COURSE' : 'COURSE.CREATE_AND_ADD_COURSE') }}
      </BaseButton>
      <BaseButton @click="submitAndRedirect">
        {{ $t(userData.id ? 'COMMON.EDIT' : 'COMMON.CREATE') }}
      </BaseButton>
    </LayoutPageActions>
    <div class="container">
      <FormGroup
        icon="file-alt"
        :title="$t('COURSE.FORM.STEP-1')"
      >
        <UsersBasicInfo
          v-model="userData"
          :company-list="companyOptions"
          :timezone-list="timeZoneOptions"
          :locale-list="localeOptions"
        />
      </FormGroup>
      <FormGroup
        v-if="userData.extra.length"
        icon="file-archive"
        :title="$t('ANNOUNCEMENT_OBSERVATION.EXTRA_INFO')"
      >
        <div class="extraFieldsContainer">
          <DynamicInput
            v-for="extra in userData.extra"
            :key="extra.key"
            v-model="extra.value"
            :item="extra"
          />
        </div>
      </FormGroup>
      <FormGroup
        icon="shield"
        :title="$t('ANNOUNCEMENT_OBSERVATION.SECURITY_INFO')"
      >
        <div class="securityForm">
          <BaseSelect
            v-model="userData.roles"
            multiselect
            name="role"
            :label="$t('USER.ROLES.TITLE')"
            :options="roleOptions"
            :error="userData.errors?.roles"
          />
          <BaseInput
            v-model="userData.password"
            :label="$t('USER.LABEL.PASSWORD')"
            :error="userData.errors?.password"
            name="password"
            type="password"
          />
        </div>
      </FormGroup>
    </div>
  </div>
</template>

<script setup>
import { useFormComposable } from '@/contexts/users/composables/userForm.composable.js'
import LayoutPageTitle from '@/contexts/shared/components/LayoutPageTitle.vue'
import LayoutPageActions from '@/contexts/shared/components/LayoutPageActions.vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import FormGroup from '@/contexts/shared/components/FormGroup.vue'
import UsersBasicInfo from '@/contexts/users/components/form/UsersBasicInfo.vue'
import DynamicInput from '@/contexts/shared/components/DynamicInput.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import BaseSelect from '@/contexts/shared/components/BaseSelect.vue'

const {
  userData,
  title,
  links,
  timeZoneOptions,
  roleOptions,
  companyOptions,
  localeOptions,
  submitAndReset,
  submitAndRedirect,
} = useFormComposable()
</script>

<style scoped lang="scss">
.UserFormView {
  .container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 2rem 0;

    .extraFieldsContainer {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
    }

    .securityForm {
      display: grid;
      gap: 2rem;
      grid-template-columns: 2fr 1fr;
    }
  }
  :deep(.BaseInput) {
    .input {
      height: 2.3rem;

      input,
      span {
        height: 100%;
      }
    }
  }
}
</style>
