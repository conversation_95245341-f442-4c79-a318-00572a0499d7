<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\LTI\LtiPlatformMother;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\Uuid;

trait LtiPlatformFixtureTrait
{
    private function setAndGetLtiPlatformInRepository(
        ?Uuid $id = null,
        ?Uuid $registrationId = null,
        ?string $name = null,
        ?string $audience = null,
        ?Url $oidcAuthenticationUrl = null,
        ?Url $oauth2AccessTokenUrl = null,
        ?Url $jwksUrl = null,
    ): LtiPlatform {
        $platform = LtiPlatformMother::create(
            id: $id,
            registrationId: $registrationId,
            name: $name,
            audience: $audience,
            oidcAuthenticationUrl: $oidcAuthenticationUrl,
            oauth2AccessTokenUrl: $oauth2AccessTokenUrl,
            jwksUrl: $jwksUrl
        );

        $this->client->getContainer()->get('App\V2\Domain\LTI\LtiPlatformRepository')
            ->put($platform);

        return $platform;
    }
}
