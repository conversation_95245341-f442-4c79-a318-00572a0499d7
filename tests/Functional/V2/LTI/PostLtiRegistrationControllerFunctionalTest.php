<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\LTI;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\LtiEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\LtiRegistrationFixtureTrait;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PostLtiRegistrationControllerFunctionalTest extends FunctionalTestCase
{
    use LtiRegistrationFixtureTrait;
    use UserHelperTrait;

    private ?User $superAdminUser = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->superAdminUser = $this->createAndGetUser(
            roles: ['ROLE_SUPER_ADMIN'],
            email: '<EMAIL>',
        );
    }

    public function testForbidden(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiRegistrationEndpoint(),
            body: [
                'name' => 'Registration 1',
                'client_id' => 'registration-1',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testOk(): void
    {
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiRegistrationEndpoint(),
            body: [
                'name' => 'Registration 1',
                'client_id' => 'registration-1',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiRegistrationEndpoint(),
            body: [
                'name' => 'Registration 2',
                'client_id' => 'registration-1',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'Post LTI Registration error: Client ID: registration-1 must be unique',
            $content['message']
        );
    }

    #[DataProvider('provideFailScenarios')]
    public function testFailScenarios(
        array $body,
        int $expectedStatusCode,
        array $violations,
    ): void {
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );
        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiRegistrationEndpoint(),
            body: $body,
            bearerToken: $token
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertIsArray($content['metadata']);
        $metadata = $content['metadata'];
        $this->assertArrayHasKey('violations', $metadata);
        $this->assertEquals($violations, $metadata['violations']);
    }

    public static function provideFailScenarios(): \Generator
    {
        yield 'empty body' => [
            'body' => [],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[client_id]' => 'This field is missing.',
            ],
        ];

        yield 'empty values' => [
            'body' => [
                'name' => '',
                'client_id' => '',
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[client_id]' => 'This value should not be blank.',
            ],
        ];

        yield 'different datatypes' => [
            'body' => [
                'name' => 1,
                'client_id' => 5,
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'violations' => [
                '[name]' => 'This value should be of type string.',
                '[client_id]' => 'This value should be of type string.',
            ],
        ];
    }

    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds([$this->superAdminUser->getId()]);
        parent::tearDown();
    }
}
