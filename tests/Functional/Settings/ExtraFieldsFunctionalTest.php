<?php

declare(strict_types=1);

namespace App\Tests\Functional\Settings;

use App\Entity\Setting;
use App\Service\SettingsService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendSettingsEndpoints;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class ExtraFieldsFunctionalTest extends FunctionalTestCase
{
    private string $defaultExtraFields = '';

    /**
     * @throws NotSupported
     */
    public function setUp(): void
    {
        parent::setUp();
        $settingsRepository = $this->getRepository(Setting::class);
        $userExtraFieldsSetting = $settingsRepository->findOneBy(['code' => 'app.user.extra_fields']);
        $this->defaultExtraFields = $userExtraFieldsSetting->getValue();
    }

    public function testSettingsExtraFields()
    {
        $this->assertBase();
    }

    /**
     * @dataProvider settingsExtraFieldsProvider
     */
    public function testSetUserExtraFields($extraFields, $expectedLabel, $expectedOptions, $locale = null): void
    {
        $userToken = $this->loginAndGetToken();
        $settings = $this->client->getContainer()->get(SettingsService::class);
        $settings->setSetting('app.user.extra_fields', json_encode($extraFields));
        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendSettingsEndpoints::settingsWithLocaleEndpoint($locale),
            [],
            [],
            [],
            $userToken,
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->assertBase($locale);

        foreach ($responseData as $key => $extraField) {
            $this->assertArrayHasKey('name', $extraField);
            $this->assertArrayHasKey('required', $extraField);
            $this->assertArrayHasKey('label', $extraField);
            $this->assertArrayHasKey('type', $extraField);
            $this->assertEquals($expectedLabel[$key], $extraField['label']);
            $this->assertEquals($expectedOptions[$key], $extraField['options']);
            // $this->assertStringContainsString('options', $extraField);
        }
    }

    public static function settingsExtraFieldsProvider(): \Generator
    {
        yield '1 user extra field' => [
            'extraFields' => [
                self::getDniExtraField(),
            ],
            'expectedLabel' => ['DNI'],
            'expectedOptions' => [null],
        ];

        yield '1 user extra field in english' => [
            'extraFields' => [
                self::getDniExtraField()
            ],
            'expectedLabel' => ['ID'],
            'expectedOptions' => [null],
            'locale' => 'en'
        ];

        yield '1 user extra field in non existing language' => [
            'extraFields' => [
                self::getDniExtraField()
            ],
            'expectedLabel' => ['DNI'],
            'expectedOptions' => [null],
            'locale' => 'pt'
        ];

        yield '1 user extra field with 2 options' => [
            'extraFields' => [
                self::getSelectExtraField(),
            ],
            'expectedLabel' => ['Selector de entrada'],
            'expectedOptions' => [
                [
                    [
                        'value' => '1',
                        'name' => 'Opción 1',
                    ],
                    [
                        'value' => '2',
                        'name' => 'Opción 2',
                    ]
                ]
            ],
            'locale' => 'es',
        ];

        yield '2 user extra field. 1 with 2 options' => [
            'extraFields' => [
                self::getSelectExtraField(),
                self::getDniExtraField()
            ],
            'expectedLabel' => ['Selector de entrada', 'DNI'],
            'expectedOptions' => [
                [
                    [
                        'value' => '1',
                        'name' => 'Opción 1',
                    ],
                    [
                        'value' => '2',
                        'name' => 'Opción 2',
                    ]
                ],
                null
            ],
            'locale' => 'es',
        ];
    }

    protected function assertBase($locale = null): array
    {
        $userToken = $this->loginAndGetToken();
        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendSettingsEndpoints::settingsWithLocaleEndpoint($locale),
            [],
            [],
            [],
            $userToken,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        return $this->extractResponseData($response);
    }

    private static function getDniExtraField(): array
    {
        return [
            'name' => 'dni',
            'required' => true,
            'label' => [
                'default' => 'DNI',
                'translations' => [
                    [
                        'language' => 'es',
                        'value' => 'DNI',
                    ],
                    [
                        'language' => 'en',
                        'value' => 'ID',
                    ],
                ],
            ],
            'type' => 'text',
            // Return must be null because of the type.
            'options' => [
                [
                    'value' => '1',
                    'name' => [
                        'default' => 'Option 1',
                        'translations' => [
                            [
                                'language' => 'es',
                                'value' => 'Opción 1',
                            ],
                            [
                                'language' => 'en',
                                'value' => 'Option 1',
                            ],
                        ],
                    ]
                ],
            ]
        ];
    }

    private static function getSelectExtraField(): array
    {
        return [
            'name' => 'input select',
            'required' => true,
            'label' => [
                'default' => 'Input select',
                'translations' => [
                    [
                        'language' => 'es',
                        'value' => 'Selector de entrada',
                    ],
                    [
                        'language' => 'en',
                        'value' => 'Input select',
                    ],
                ],
            ],
            'type' => 'select',
            'options' => [
                [
                    'value' => '1',
                    'name' => [
                        'default' => 'Option 1',
                        'translations' => [
                            [
                                'language' => 'es',
                                'value' => 'Opción 1',
                            ],
                            [
                                'language' => 'en',
                                'value' => 'Option 1',
                            ],
                        ],
                    ]
                ],
                [
                    'value' => '2',
                    'name' => [
                        'default' => 'Option 2',
                        'translations' => [
                            [
                                'language' => 'es',
                                'value' => 'Opción 2',
                            ],
                            [
                                'language' => 'en',
                                'value' => 'Option 2',
                            ],
                        ],
                    ]
                ],
            ],
        ];
    }

    /**
     * @throws NotSupported
     * @throws ORMException
     * @throws MappingException
     */
    public function TearDown(): void
    {
        $settingsRepository = $this->getRepository(Setting::class);
        $userExtraFieldsSetting = $settingsRepository->findOneBy(['code' => 'app.user.extra_fields']);
        $userExtraFieldsSetting->setValue($this->defaultExtraFields);

        $em = $this->getEntityManager();
        $em->flush();

        parent::tearDown();
    }
}
