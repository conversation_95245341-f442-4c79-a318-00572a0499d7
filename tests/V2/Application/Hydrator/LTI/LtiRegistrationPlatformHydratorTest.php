<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\LTI;

use App\Tests\V2\Mother\LTI\LtiPlatformMother;
use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\LTI\LtiRegistrationPlatformHydrator;
use App\V2\Domain\LTI\LtiPlatformCollection;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class LtiRegistrationPlatformHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?LtiPlatformRepository $ltiPlatformRepository = null,
    ): LtiRegistrationPlatformHydrator {
        return new LtiRegistrationPlatformHydrator(
            ltiPlatformRepository: $ltiPlatformRepository ?? $this->createMock(LtiPlatformRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                LtiRegistrationHydrationCriteria::createEmpty()
                    ->withPlatform()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                LtiRegistrationHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     * @throws CollectionException
     * @throws Exception
     */
    public function testEmptyCollection(): void
    {
        $collection = new LtiRegistrationCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            LtiRegistrationHydrationCriteria::createEmpty()
                ->withPlatform()
        );
        $this->assertEmpty($collection->all());
    }

    /**
     * @throws InfrastructureException
     * @throws Exception
     * @throws HydratorException
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    public function testHydrateWithPlatform(): void
    {
        $registration1 = LtiRegistrationMother::create();
        $registration2 = LtiRegistrationMother::create();
        $registration3 = LtiRegistrationMother::create();

        $platform1 = LtiPlatformMother::create(registrationId: $registration1->getId());
        $platform2 = LtiPlatformMother::create(registrationId: $registration2->getId());
        $platform3 = LtiPlatformMother::create(registrationId: $registration3->getId());

        $collection = new LtiRegistrationCollection([$registration1, $registration2, $registration3]);

        $ltiPlatformRepository = $this->createMock(LtiPlatformRepository::class);
        $ltiPlatformRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new LtiPlatformCollection([$platform1, $platform2, $platform3]));

        $hydrator = $this->getHydrator(
            ltiPlatformRepository: $ltiPlatformRepository
        );

        $hydrator->hydrate(
            $collection,
            LtiRegistrationHydrationCriteria::createEmpty()
                ->withPlatform()
        );

        $this->assertNotNull($registration1->getPlatform());
        $this->assertNotNull($registration2->getPlatform());
        $this->assertNotNull($registration3->getPlatform());

        $this->assertEquals($platform1, $registration1->getPlatform());
        $this->assertEquals($platform2, $registration2->getPlatform());
        $this->assertEquals($platform3, $registration3->getPlatform());
    }
}
