<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Course\Diploma;

use App\V2\Domain\Course\Diploma\DiplomaConfig;
use PHPUnit\Framework\TestCase;

class DiplomaConfigTest extends TestCase
{
    public function testToArray(): void
    {
        $diplomaConfig = new DiplomaConfig(true);
        $array = $diplomaConfig->toArray();

        $this->assertArrayHasKey('show_duration', $array);
        $this->assertTrue($array['show_duration']);

        $diplomaConfig = new DiplomaConfig(false);
        $array = $diplomaConfig->toArray();

        $this->assertFalse($array['show_duration']);
    }

    public function testFromArray(): void
    {
        $array = ['show_duration' => true];
        $diplomaConfig = DiplomaConfig::fromArray($array);

        $this->assertInstanceOf(DiplomaConfig::class, $diplomaConfig);
        $this->assertTrue($diplomaConfig->showDuration());

        $array = ['show_duration' => false];
        $diplomaConfig = DiplomaConfig::fromArray($array);

        $this->assertFalse($diplomaConfig->showDuration());

        $array = [];
        $diplomaConfig = DiplomaConfig::fromArray($array);

        $this->assertFalse($diplomaConfig->showDuration());
    }
}
