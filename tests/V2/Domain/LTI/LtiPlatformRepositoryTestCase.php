<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\LTI;

use App\Tests\V2\Mother\LTI\LtiPlatformMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\Exceptions\LtiPlatformNotFoundException;
use App\V2\Domain\LTI\LtiPlatformCollection;
use App\V2\Domain\LTI\LtiPlatformCriteria;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class LtiPlatformRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): LtiPlatformRepository;

    /**
     * @throws LtiPlatformNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testPut(): void
    {
        $registrationId1 = UuidMother::create();
        $registrationId2 = UuidMother::create();
        $registrationId3 = UuidMother::create();

        $platform1 = LtiPlatformMother::create(registrationId: $registrationId1, name: 'Platform 1');
        $platform2 = LtiPlatformMother::create(registrationId: $registrationId2, name: 'Platform 2');
        $platform3 = LtiPlatformMother::create(registrationId: $registrationId3, name: 'Platform 3');

        $repository = $this->getRepository();

        $repository->put($platform1);
        $repository->put($platform2);
        $repository->put($platform3);

        $this->assertEquals(
            $platform2,
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterById($platform2->getId())
                    ->filterByRegistrationId($platform2->getRegistrationId())
            )
        );

        $this->assertEquals(
            $platform1,
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws LtiPlatformNotFoundException
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public function testFindOneBy(): void
    {
        $registrationId1 = UuidMother::create();
        $registrationId2 = UuidMother::create();
        $registrationId3 = UuidMother::create();

        $platform1 = LtiPlatformMother::create(registrationId: $registrationId1);
        $platform2 = LtiPlatformMother::create(registrationId: $registrationId2, audience: 'audience 2');
        $platform3 = LtiPlatformMother::create(registrationId: $registrationId3);

        $repository = $this->getRepository();

        $repository->put($platform1);
        $repository->put($platform2);
        $repository->put($platform3);

        $this->assertEquals(
            $platform2,
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterById($platform2->getId())
                    ->filterByRegistrationId($registrationId2)
            )
        );

        $this->assertEquals(
            $platform3,
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterByRegistrationId($registrationId3)
            )
        );

        $this->assertEquals(
            $platform2,
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterByAudience('audience 2')
            )
        );
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        LtiPlatformCollection $input,
        LtiPlatformCriteria $criteria,
        int $expectedCount,
        array $expectedResults
    ) {
        $repository = $this->getRepository();
        foreach ($input->all() as $platform) {
            $repository->put($platform);
        }

        $result = $repository->findBy(
            LtiPlatformCriteria::createEmpty()
        );
        $this->assertCount(
            0,
            array_diff(
                $input->all(),
                $result->all(),
            )
        );

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);
        $this->assertCount(
            0,
            array_diff(
                $expectedResults,
                $result->all(),
            )
        );
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public static function provideFindBy(): \Generator
    {
        $platform1 = LtiPlatformMother::create();
        $platform2 = LtiPlatformMother::create();
        $platform3 = LtiPlatformMother::create();

        yield '3 platforms get all' => [
            'input' => new LtiPlatformCollection([$platform1, $platform2, $platform3]),
            'criteria' => LtiPlatformCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$platform1, $platform2, $platform3],
        ];

        yield '3 platforms platform by id' => [
            'input' => new LtiPlatformCollection([$platform1, $platform2, $platform3]),
            'criteria' => LtiPlatformCriteria::createEmpty()->filterById($platform2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$platform2],
        ];

        yield '3 platforms platform by id 1 and id 3' => [
            'input' => new LtiPlatformCollection([$platform1, $platform2, $platform3]),
            'criteria' => LtiPlatformCriteria::createEmpty()
                ->filterByIds(new UuidCollection([$platform1->getId(), $platform3->getId()])),
            'expectedCount' => 2,
            'expectedResults' => [$platform1, $platform3],
        ];

        yield '3 platforms platform by registration id' => [
            'input' => new LtiPlatformCollection([$platform1, $platform2, $platform3]),
            'criteria' => LtiPlatformCriteria::createEmpty()->filterByRegistrationId($platform3->getRegistrationId()),
            'expectedCount' => 1,
            'expectedResults' => [$platform3],
        ];

        yield '3 platforms platform by id and registration id' => [
            'input' => new LtiPlatformCollection([$platform1, $platform2, $platform3]),
            'criteria' => LtiPlatformCriteria::createEmpty()
                ->filterById($platform1->getId())
                ->filterByRegistrationId($platform1->getRegistrationId()),
            'expectedCount' => 1,
            'expectedResults' => [$platform1],
        ];

        yield '3 platforms filter by two registrations' => [
            'input' => new LtiPlatformCollection([$platform1, $platform2, $platform3]),
            'criteria' => LtiPlatformCriteria::createEmpty()
                ->filterByRegistrationIds(
                    new UuidCollection([$platform1->getRegistrationId(), $platform3->getRegistrationId()])
                ),
            'expectedCount' => 2,
            'expectedResults' => [$platform1, $platform3],
        ];
    }

    /**
     * @throws LtiPlatformNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testDelete(): void
    {
        $platform1 = LtiPlatformMother::create();
        $platform2 = LtiPlatformMother::create();
        $platform3 = LtiPlatformMother::create();

        $repository = $this->getRepository();

        $repository->put($platform1);
        $repository->put($platform2);
        $repository->put($platform3);

        $this->assertEquals(
            $platform2,
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterById($platform2->getId())
            )
        );

        $repository->delete($platform2);
        try {
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterById($platform2->getId())
            );

            $this->fail('Exception was not thrown');
        } catch (LtiPlatformNotFoundException) {
        }

        $this->assertEquals(
            $platform3,
            $repository->findOneBy(
                LtiPlatformCriteria::createEmpty()
                    ->filterById($platform3->getId())
                    ->filterByRegistrationId($platform3->getRegistrationId())
            )
        );
    }
}
