<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Course\Creator;

use App\Tests\V2\Domain\Course\Creator\CourseCreatorRepositoryTestCase;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Infrastructure\Course\Creator\InMemoryCourseCreatorRepository;

class InMemoryCourseCreatorRepositoryTest extends CourseCreatorRepositoryTestCase
{
    protected function getRepository(): CourseCreatorRepository
    {
        return new InMemoryCourseCreatorRepository();
    }
}
