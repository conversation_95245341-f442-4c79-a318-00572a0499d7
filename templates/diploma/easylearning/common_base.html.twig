<link href="file://{{ build_dir }}/diploma.css" rel="stylesheet">

<div class="fondo" style="background-image: url('{{ assets_dir }}/diploma/easylearning/footer_diploma.svg');">
  <div class="head">
    <img src="{{ assets_dir }}/diploma/easylearning/logo_diploma.png" width="16rem" class="logoDiploma" />
{#    <img src="./assets/diploma/logo_diploma.png" width="16rem" class="logoDiploma" />#}
    <p class="textDiploma">{{ 'message_api.diploma.diploma'|trans({}, 'message_api', locale) }}</p>
  </div>

  <div class="bodyDiploma">
    <p class="concedido">{{ 'message_api.diploma.granted'|trans({}, 'message_api', locale) }}:</p>
    <p class="profile-name">
      <strong>{{ firstName }} {{ lastName }}</strong>
    </p>

   

    <p class="profile-supered">{{ 'message_api.diploma.supered'|trans({}, 'message_api', locale) }}</p>
    <p class="profile-course">
      <strong>{{ nameCourse }}</strong>
    </p>

    <p class="profile-date">{{ 'message_api.diploma.date'|trans({}, 'message_api', locale) }}: {{ date }}</p>
    {% if showDuration %}
    <p class="profile-duration">{{ 'message.api.diploma.duration'|trans({}, 'message_api', locale) }}: 35 {{ 'message_api.diploma.hours_unit'|trans({}, 'message_api', locale) }}</p>
    {% endif %}
  </div>
</div>