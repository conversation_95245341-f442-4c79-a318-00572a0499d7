<div class="content-panel pb-3">
  {% if course.isCompletedContentChapter or (course.typeCourse and (course.typeCourse.id == 2 or course.typeCourse.id == 4)) %}
    <div class="page-actions pt-3 pr-3 text-right">
      <a class="action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementCrudController').setAction('index').set('origin', 'course').set('id', course.id).set('activeRoute', 'CreateAnnouncement').set('referrer', referrerAnnouncement) }}">{{ 'course.configureFields.add_annuncement'|trans({}, 'messages', app.user.locale) }}</a>
    </div>
  {% else %}
    <div class="page-actions pt-3 pr-3 text-right">
      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#announcementModal">{{ 'course.configureFields.add_annuncement'|trans({}) }}</button>
    </div>

    <div class="modal fade" id="announcementModal" tabindex="-1" aria-labelledby="announcementModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="announcementModalLabel">{{ 'announcements.label_in_singular'|trans({}) }}</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-3">
                <img style="width:5rem" src="{{ asset('assets/chapters/advertencia.svg') }}" />
              </div>
              <div class="col-md-9">
                <p>{{ 'message_api.alert.course_content_incomplete'|trans({}, 'message_api') }}</p>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ 'Close'|trans({}, 'messages') }}</button>
          </div>
        </div>
      </div>
    </div>
  {% endif %}

  {% if course.announcements|length %}
    <div class="content-panel-body with-rounded-top course-announcement">
      <table class="table datagrid with-rounded-top" data-sort-url="{{ path('admin_season_order') }}">
        <thead>
          <tr>
            <th>
              <span>{{ 'announcements.configureFields.code'|trans({}, 'messages', app.user.locale) }}</span>
            </th>
            <th>
              <span>{{ 'announcements.configureFields.start_at'|trans({}, 'messages', app.user.locale) }}</span>
            </th>
            <th>
              <span>{{ 'announcements.configureFields.finish_at'|trans({}, 'messages', app.user.locale) }}</span>
            </th>
            <th>
              <span class="sr-only">Actions</span> 
            </th>
          </tr>
        </thead>
        <tbody>
          {% set count = 0 %}
          {% for announcement in course.announcements %}

              {%  set count = count + 1 %}
              <tr data-id="{{ announcement.id }}">
                <td class="announcement-code">{{ announcement.code }}</td>
                <td class="announcement-name">{{ announcement.startAt|date('m/d/Y')|format_datetime('full', 'none', locale = app.user.locale) }}</td>
                <td class="announcement-name">{{ announcement.finishAt|date('m/d/Y')|format_datetime('full', 'none', locale = app.user.locale) }}</td>
                <td class="text-right">
                  <a class="btn btn-success" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementCrudController').setAction('index').set('id', announcement.id).set('activeRoute', 'ViewAnnouncement').set('origin', 'course').set('referrer', referrerAnnouncement) }}"><i class="fa fa-eye text-white"></i></a>

                  <a class="btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementCrudController').setAction('index').set('id', announcement.id).set('activeRoute', 'UpdateAnnouncement').set('origin', 'course').set('referrer', referrerAnnouncement) }}"><i class="fa fa-pencil text-white"></i></a>

                  <a class="btn btn-danger action-delete" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementCrudController').setAction('delete').setEntityId(announcement.id).set('referrer', referrerAnnouncementDelete) }}" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementCrudController').setAction('delete').setEntityId(announcement.id).set('referrer', referrerAnnouncementDelete) }}" data-bs-toggle="modal" data-bs-target="#modal-delete"><i class="fa fa-trash text-white"></i></a>
                </td>
              </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    <div class="content-panel-footer without-padding without-border">
      <div class="list-pagination">
        <div class="list-pagination-counter">
          <strong>{{ count }}</strong> results
        </div>
      </div>
    </div>
  {% else %}
    <div class="mt-5">
      <div class="card text-center">
        <div class="card-header" style="height:20rem">
          <h4 style="padding-top:8rem">{{ 'no_content'|trans({}, 'messages') }}</h4>
        </div>
      </div>
    </div>
  {% endif %}
</div>


