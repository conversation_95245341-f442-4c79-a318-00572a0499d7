<div class="pdf" style="margin-top: 20rem;">
    <div class="row row-title">
        <div class="col-12 mt-4">
            <h2 style="border: 1px solid #37474F; border-radius: 7px; padding: 1rem; text-align: center;">
                <i class="fas fa-clock"></i> {{ 'segmented_stats.title2'|trans({}, 'messages',  app.user.locale) }}
            </h2>
        </div>
    </div>
    <div class="row-panel">
        <div class="row">
            <div class="col-12 mt-4" style="margin-inline: auto;">
                <line-chart-double :series-data="data.hours.doubleLineChart"></line-chart-double>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12"><h4 style="text-align: center;">{{ 'segmented_stats.title1'|trans({}, 'messages',  app.user.locale) }}</h4></div>
            <div class="col-6">
                <h4 style="text-align: center;"><i class="fas fa-users"></i> {{ 'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}</h4>
                <pie-chart :series-data="data.hours.pieChart.totals" :colors="['#80CBC4', '#AED581', '#009688']" :inner-size="'80%'"></pie-chart>
            </div>
            <div class="col-6">
                <h4 style="text-align: center;"><i class="fas fa-users"></i> {{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}</h4>
                <bar-chart :series-data="data.hours.pieChart.avg" :colors="['#80CBC4', '#AED581', '#009688']"></bar-chart>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12" style="margin-top: 11rem;">
                <h4 style="text-align: center;"> <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}]</h4>
                <bar-chart :series-data="data.hours.distribution.total" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
            <div class="col-12 mt-2">
                <h4 style="text-align: center;"> <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}]</h4>
                <bar-chart :series-data="data.hours.distribution.avg" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h4 style="text-align: center;"><i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.structure'|trans({}, 'messages',  app.user.locale) }})</h4>
            </div>
            <div class="col-12">
                <h4 style="text-align: center;"><i class="fas fa-clock"></i>
                    {{'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}:
                    ${formatNumber(data.hours.department.totalStructure)}</h4>
                <bar-chart :series-data="data.hours.structure.data"></bar-chart>
            </div>
            <div class="col-12 mt-4">
                <h4 style="text-align: center;" class="mt-2"><i class="fas fa-clock"></i>
                    {{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}:
                    ${formatNumber(data.hours.department.totalStructureAVG)}</h4>
                <bar-chart :series-data="data.hours.structure.avg"></bar-chart>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12"><h4 style="text-align: center;"><i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                    ({{ 'segmented_stats.hotel'|trans({}, 'messages',  app.user.locale) }})</h4></div>
            <div class="col-12">
                <h4 style="text-align: center;">
                    <i class="fas fa-clock"></i>
                    {{'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}:
                    ${formatNumber(data.hours.department.totalStructure)}</h4>
                <bar-chart :series-data="data.hours.hotel.data" :colors="['#E57373', '#EF9A9A']"></bar-chart>
            </div>
            <div class="col-12 mt-2">
                <h4 style="text-align: center;">
                    <i class="fas fa-clock"></i>
                    {{'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}:
                    ${formatNumber(data.hours.department.totalStructureAVG)}</h4>
                <bar-chart :series-data="data.hours.hotel.avg" :colors="['#E57373', '#EF9A9A']"></bar-chart>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12 mt-4"><h4 style="text-align: center;">
                    <i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.by_school'|trans({}, 'messages',  app.user.locale) }}
                </h4></div>
            <div class="col-12">
                <h4 style="text-align: center;">
                    <i class="fas fa-clock"></i>
                    {{'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}</h4>
                <bar-chart :series-data="data.hours.school" :colors="defaultColors"></bar-chart>
            </div>
            <div class="col-12 mt-2">
                <h4 style="text-align: center;">
                    <i class="fas fa-clock"></i>
                    {{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}
                </h4>
                <bar-chart :series-data="data.hours.schoolAVG" :colors="defaultColors"></bar-chart>
            </div>
        </div>
    </div>
</div>
