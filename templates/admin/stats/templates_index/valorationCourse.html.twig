<div v-if="general && ((general.npsTopCourses && general.npsTopCourses.length) || (general.npsWorstCourses && general.npsWorstCourses.length) || (general.mostPopularsCourse && general.mostPopularsCourse.length))" class="nps">
	<div v-if="general.npsTopCourses && general.npsTopCourses.length" class="course-list">
		<h2 class="subtitle">
			<i class="far fa-thumbs-up"></i>
			{{ 'stats.top_rated_courses'|trans({}, 'messages',  app.user.locale) }}
		</h2>
		<ol>
			<li v-for="course in general.npsTopCourses">
				<span v-if="course.chart_by_nps" class="success nps-percentaje">${npsFormatted(course.nps)}</span>
				<span v-else class="success nps-star-avg">
					<i class="fa fa-star"></i> ${course.stars}
				</span>
				<a :href="course.link">	${course.name}</a>
			</li>
		</ol>
	</div>

	<div v-if="general.npsWorstCourses && general.npsWorstCourses.length" class="course-list">
		<h2 class="subtitle">
			<i class="far fa-thumbs-down"></i>
		{{ 'stats.lowest_rated_courses'|trans({}, 'messages',  app.user.locale) }}
		</h2>
		<ol>
			<li v-for="course in general.npsWorstCourses">
				<span v-if="course.chart_by_nps" class="error nps-percentaje">${npsFormatted(course.nps)}</span>
				<span v-else class="error nps-star-avg">
					<i class="fa fa-star"></i> ${course.stars}
				</span>
				<a :href="course.link">	${course.name}</a>
			</li>
		</ol>
	</div>

	<div v-if="general.mostPopularsCourse && general.mostPopularsCourse.length" class="course-list">
		<h2 class="subtitle">
			<i class="far fa-thumbs-up"></i>
			{{ 'stats.most_completed_courses'|trans({}, 'messages',  app.user.locale) }}
		</h2>
		<ol>
			<li v-for="course in general.mostPopularsCourse">
				<span class="success nps-percentaje">${course.count}</span>
				<a :href="course.link">	${course.name}</a>
			</li>
		</ol>
	</div>
</div>
