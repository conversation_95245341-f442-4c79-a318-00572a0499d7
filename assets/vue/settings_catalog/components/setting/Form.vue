<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import Multiselect from "vue-multiselect";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Multiselect},
  data() {
    return {
      locale: 'es',
      setting: {
        id: -1,
        code: '',
        value: '',
        name: '',
        group: '',
        sort: '',
        options: '',
        type: '',     
        groups: [],  
      },
      defaultValue: { id: '', title: '' },//contiene el elemento seleccionado
      optionsType:['array', 'string','json', 'bool', 'integer'],
      defaultType:'string',
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    if (this.catalogs.length === 0) {
      this.returnToList();
      return;
    }

    let setting= {
        id: -1,
        code: '',
        value: '',
        name: '',
        group: '',
        sort: '',
        options: '',
        type: '',   
        groups: [],
      }
    
    if (this.$route.name === 'SettingUpdate') {
      setting = this.catalogs.find(c => c.id === this.$route.params.id);
      this.defaultValue.id = setting.defaultValue.id;
      this.defaultValue.title = setting.defaultValue.title;
      this.defaultType = setting.type;

      if (setting === undefined) {
        this.returnToList();
        return;
      }
    }

    this.setting = setting;

    if (this.$route.name === 'SettingCreate') {
      this.setting.groups = this.catalogs[0].groups;
    }

  },
  methods: {
    returnToList() {
      this.$router.push({name: 'Setting', params: this.$route.params});
    },
    submit() {
      const update = this.$route.name === 'SettingUpdate';
      const endpoint = update ? '/admin/setting/update' : '/admin/setting/create';
      this.setting.settingGroup = this.defaultValue.id;
      this.setting.type = this.defaultType;
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.setting });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('CODE') }}</label>
        <input type="text" class="form-control" v-model="setting.code">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.SETTING.VALUE') }}</label>
        <input type="text" class="form-control" v-model="setting.value">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="setting.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.SETTING.GROUP') }}</label>
        <multiselect v-model="defaultValue" 
          :options="setting.groups" 
          placeholder="Select one" 
          label="title" 
          track-by="title">
        </multiselect> 
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.SETTING.SORT') }}</label>
        <input type="text" class="form-control" v-model="setting.sort">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOG.SETTING.OPTIONS') }}</label>
        <input type="text" class="form-control" v-model="setting.options">
      </div>      

      <div class="form-group col-12">
        <label>{{ $t('TYPE') }}</label>
        <multiselect v-model="defaultType" 
          :options="optionsType" 
          placeholder="Select one" 
        >
        </multiselect> 
      </div>        

      <div class="form-group col-12">
        <label>{{ $t('DESCRIPTION') }}</label>      
        <textarea
            type="text"
            class="form-control"
            name="description"
            v-model="setting.description"
            rows="5"
          />
      </div>

    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
