<script>
  import BaseForm from "../BaseForm.vue";
  import { get } from "vuex-pathify";
  import BaseSwitch from "../../../base/BaseSwitch.vue";
  
  export default {
    name: "ExtraDataForm",
    components: { BaseSwitch, BaseForm },
    data() {
      return {
        locale: 'es',
        extraData: {
          id: -1,
          name: '',
          description: '',
          active: false,
          translations: [],
        },
      };
    },
    computed: {
      catalogs: get('catalogModule/catalogs'),
      locales: get('localeModule/locales'),
    },
    created() {
      let extraData = {
        id: -1,
        name: '',
        description: '',
        active: false,
        translations: [],
      };
  
      if (this.$route.name === 'ExtraDataUpdate') {
        extraData = this.catalogs.find(c => c.id === this.$route.params.id);
        if (extraData === undefined) {
          this.returnToList();
          return;
        }
      }

        
      const translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        const translated = extraData.translations.find(e => e.locale === k);
        translations.push({
          locale: k,
          name: translated?.name ?? '',
          description: translated?.description ?? '',
        });
      });
  
      extraData.translations = translations;
      this.extraData = extraData;
    },
    methods: {
      returnToList() {
        this.$router.push({ name: 'ExtraData', params: this.$route.params });
      },
  
      submit() {
        const update = this.$route.name === 'ExtraDataUpdate';
        const endpoint = update ? '/admin/extra-data/update' : '/admin/extra-data/create';
        const save = () => {
          return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.extraData });
        };
  
        save().then(r => {
          const { error, data } = r;
          if (error) this.$toast.error(data);
          else {
            this.$toast.success(this.$t('CATALOG.SAVED'));
            this.returnToList();
          }
        });
      },
    },
  };
  </script>
  
  <template>
    <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
      <template v-slot:form>
        <div class="form-group col-12">
          <label>{{ $t('NAME') }}</label>
          <input type="text" class="form-control" v-model="extraData.name">
        </div>
  
        <div class="form-group col-12">
          <label>{{ $t('DESCRIPTION') }}</label>
          <textarea class="form-control" rows="1" v-model="extraData.description"></textarea>
        </div>
  
        <div class="form-group col-12 d-flex align-items-center justify-content-start">
          <BaseSwitch :tag="`switcher-extra-data-form-active-${extraData.id}`" v-model="extraData.active" />
          <label class="ml-1">{{ $t('ACTIVE') }}</label>
        </div>
      </template>
  
      <template v-slot:translations>
        <div v-for="t in extraData.translations" :key="t.locale" v-if="t.locale === locale">
          <div class="form-group col-12">
            <label>{{ $t('NAME') }}</label>
            <input type="text" class="form-control" v-model="t.name">
          </div>
          <div class="form-group col-12">
            <label>{{ $t('DESCRIPTION') }}</label>
            <!-- <input type="text" class="form-control" v-model="t.description"> -->
            <textarea class="form-control" rows="1" v-model="t.description"></textarea>

          </div>
        </div>
      </template>
    </base-form>
  </template>
  
  <style scoped lang="scss">
  
  </style>
  